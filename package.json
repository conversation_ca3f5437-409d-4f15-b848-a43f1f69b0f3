{"name": "captcha-bypasser", "version": "1.0.0", "description": "Automated browser login with captcha solving using SolveCaptcha API", "main": "captcha-bypasser.js", "scripts": {"start": "node captcha-bypasser.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"selenium-webdriver": "^4.15.0", "axios": "^1.6.0"}, "keywords": ["<PERSON><PERSON>a", "automation", "selenium", "browser", "login"], "author": "", "license": "MIT"}