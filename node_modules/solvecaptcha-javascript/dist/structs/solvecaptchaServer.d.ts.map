{"version": 3, "file": "solvecaptchaServer.d.ts", "sourceRoot": "", "sources": ["../../src/structs/solvecaptchaServer.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAKpD,MAAM,WAAW,mBAAmB;IAChC,IAAI,EAAE,MAAM,CAAC;IACb,EAAE,EAAE,MAAM,CAAA;CACb;AAED,MAAM,WAAW,YAAY;IACzB,WAAW,EAAE,CAAC,OAAO,EAAE,mBAAmB,KAAK,IAAI,CAAC;IACpD,UAAU,EAAE,CAAC,OAAO,EAAE,mBAAmB,KAAK,IAAI,CAAC;CACtD;AAED,MAAM,WAAW,MAAM;IACnB,EAAE,CAAC,CAAC,SAAS,MAAM,YAAY,EAC3B,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC,GACpC,IAAI,CAAC;CACX;AAED;;;;GAIG;AACH,qBAAa,MAAO,SAAQ,YAAY;IACpC,OAAO,CAAC,OAAO,CAAS;IACxB,OAAO,CAAC,WAAW,CAAS;IAE5B,OAAO,CAAC,WAAW,CAAS;IAC5B,OAAO,CAAC,WAAW,CAAS;IAC5B,OAAO,CAAC,eAAe,CAAS;IAEhC,OAAO,CAAC,WAAW,CAAkB;gBAEzB,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,UAAU,GAAE,OAAc;YAWxG,MAAM;IA8BpB,OAAO,KAAK,cAAc,GAIzB;IAED;;OAEG;IACI,SAAS;IAGhB,gBAAgB,CAAC,MAAM,EAAE,eAAe;CAO3C"}