"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const constants_1 = require("../constants/constants");
const defautlProvider = constants_1.supportedProviders.solveCaptcha.name;
function getProviderData(provider = defautlProvider) {
    const currentProvider = provider;
    let currentProviderData;
    switch (currentProvider) {
        case 'solvecaptcha':
            currentProviderData = constants_1.supportedProviders.solveCaptcha;
            break;
        // case 'opencaptcha':
        //   currentProviderData = supportedProviders.opencaptcha
        //   break;
        default:
            currentProviderData = constants_1.supportedProviders.solveCaptcha;
    }
    return currentProviderData;
}
exports.default = getProviderData;
