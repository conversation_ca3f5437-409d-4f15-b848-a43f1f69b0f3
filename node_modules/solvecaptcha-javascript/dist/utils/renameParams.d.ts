/**
 *
 * ### Renaming captcha parameters
 *
 * Description: parameter names used in the API may differ from those used in the library, in such cases parameter names are renamed in accordance with those used in the API.
 *
 * @param params - captcha parameters as an object
 * @returns returns new object with renamed params
 *
 */
export default function renameParams(params: any): any;
//# sourceMappingURL=renameParams.d.ts.map