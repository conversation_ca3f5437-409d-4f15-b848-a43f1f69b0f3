{"name": "solvecaptcha-javascript", "version": "1.0.1", "description": "Node.js wrapper for SolveCaptcha.com API. Bypass recaptcha, hcaptcha, cloudflare capthca and more.", "main": "dist/index.js", "repository": {"type": "git", "url": "git+https://github.com/solvercaptcha/solvecaptcha-javascript.git"}, "license": "MIT", "author": "Solvecaptcha", "bugs": {"url": "hhttps://github.com/solvercaptcha/solvecaptcha-javascript/issues"}, "homepage": "https://github.com/solvercaptcha/solvecaptcha-javascript", "types": "dist/index.d.ts", "keywords": ["solvecaptcha", "h<PERSON><PERSON>a", "reCAPTCHA", "FunCaptcha", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>a", "api", "cap<PERSON>asolver", "<PERSON><PERSON><PERSON>", "Geetest", "image captcha", "Coordinates", "<PERSON><PERSON>", "Geetest V4", "Yandex Smart Captcha", "<PERSON><PERSON>", "bypass captcha", "solve captcha", "Amazon WAF", "<PERSON><PERSON>", "DataDome CAPTCHA", "CyberSiARA", "MTCaptcha", "Bounding Box Method", "Friendly Captcha", "Text Captcha", "<PERSON><PERSON>", "Rotate", "KeyCaptcha", "Cutcaptcha", "Tencent", "atbCAPTCHA", "Audio Recognition"], "scripts": {"build": "tsc && node ./dist/index.js", "compile": "tsc", "jsdoc": "jsdoc", "docgen": "tsc && yarn jsdoc ./dist -R \"./readme.md\" -P \"./package.json\" -t \"./node_modules/jaguarjs-jsdoc\" -d \"./docs\" -r", "example": "tsc && node ./tests/mtCaptcha.js"}, "dependencies": {"@types/node-fetch": "^2.6.12", "form-data": "^4.0.2", "node-fetch": "^2.7.0", "undici": "^6.0.0"}, "devDependencies": {"@types/node": "^20.8.7", "dotenv": "^16.0.3", "jaguarjs-jsdoc": "^1.0.2", "jsdoc": "^4.0.2", "typescript": "^4.9.4"}}