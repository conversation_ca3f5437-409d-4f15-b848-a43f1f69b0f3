## 4.34.0

- Bumping to nightly versions
- Add warning when ftp proxy is used (#15905)
- specify min required node version to >=20.0.0
- [cdp] Add CDP for Chrome 138 and remove 135

## 4.33.0

- [cdp] Add CDP for Chrome 137 and remove 134
- Fixing JS FF test that needs to add arguments.
- Making Bidi Network, DevTools and Options test pass in RBE
- Chrome capabilities test passes now in RBE
- update old freenode channel link to libera (#15698)

## 4.32.0

- [cdp] Add CDP for Chrome 136 and remove 133
- Set remote active protocol in Firefox to BiDi only

## 4.31.0

- [bidi]: fix storage module instance checks and add user context cookie test (#15467)
- [JS] Add websocket port option in Firefox ServiceBuilder when '--connect-existing' is not passed (#15557)
- [cdp] add support for 135 and remove 132

## 4.30.0

- [bidi] fix chrome and firefox test for CI RBE (#15405)
- [cdp] add support for 134 and remove 131
- [cdp] remove support for v85 since no longer required by Firefox
- [bidi] implement permissions module commands in JS (#15304)

## 4.29.0

- Add CDP for Chrome 133 and remove 130
- [js] Remove Firefox CDP (#15200)
- [js][bidi] Implement bidi `setCacheBehavior` command (#15136)
- [js][bidi] Implement bidi getClientWindows command in browser module (#15248)

## 4.28.1

- [JS] specify min required node versiton to 18.20.5

## 4.28.0

- Add CDP for Chrome 132 and remove 129
- [js] Add Federated Credential Management support (#15008)
- [JS] Specify Node.js engine version as 22.x LTS in package.json (#15058)
- [JS] Add detailed error message for invalid cookie name validation in getCookie method
- [JS] Add err message for invalid cookie name in deleteCookie method
- [JS] Enables diagnostic logging for Safari
- [java][JS][py] Add straight relative-by locators (#14482)

## 4.27.0

- Add CDP for Chrome 131 and remove 128
- Add Firefox CDP deprecation warnings
- Update supported versions for Chrome DevTools
- [bidi] Allow passing in uri for authentication handlers (#14386)
- [bidi] Enable locate node tests for Chrome and Edge
- [bidi] Ensure start nodes are serialized

## 4.26.0

- Add CDP for Chrome 130 and remove 127
- Fix sendKeys command fail on FileDetector.handleFile error (#14663)
- Update dependencies to latest versions to resolve security alerts
- Close BiDi websocket connection (#14507)

## 4.25.0

- Add CDP for Chrome 129 and remove 126

## 4.24.1

- Close CDP websocket connection on driver.quit (#14501)

## 4.24.0

- [js] expose selenium version for node.js (#14325)
- [bidi] Add authentication handlers
- [bidi] Add high-level script command (#14293)
- Handle optional dependency for @bazel/runfiles
- remove console msg and safe exit if runfiles never found
- fix rbe build(use global node process)
- Fix error handling for missing runfiles
- [ci] Use a tag to figure out what we might want to release (#14378)
- Add CDP for Chrome 128 and remove 125

## 4.23.0

- Expose pnpm as a tool we can use
- [bidi] Fix the event unsubscribe method. Update modules to have close methods. (#14192)
- Run Node browser tests on the RBE (#14194)
- [bidi] Add methods to add/remove handlers in Script module (#14230)
- [bidi] Add source type to log entry (#14244)
- [bidi] Add dom mutation handlers (#14238)
- [bidi] Add high-level script pinning APIs (#14250)
- [bidi] Deprecate argument value wrapper class (#14251)
- Add CDP for Chrome 127 and remove 124

## 4.22.0

- [bidi] Add types for user prompt related events (#14097)
- Add preference to enable CDP in Firefox by default (#14091)
- [bidi] Add callback handlers for logging APIs (#14120)
- [bidi] Add high-level logging API (#14135)
- Add CDP for Chrome 126 and remove 123

## 4.21.0

- Add CDP for Chrome 125 and remove 122
- Ensure 'selectVisibleByText' method is same as other languages (#13899)
- Ensure parity in the locators used by methods (#13902)

## 4.20.0

- Add CDP for Chrome 124 and remove 121
- [bidi] Update capture screenshot APIs to include all parameters and remove scroll parameter (
  #13744)
- [bidi] Implement functionality to retrieve all top-level browsing contexts
- Set browserName by default when browserOptions are used
- Implement fullPageScreenshot functionality for Firefox (#13301)
- Nightly JS builds are now pushed to GitHub packages
- Making Selenium Manager a thin wrapper (#13853)
  - This change has been made to make it easier to maintain and improve, the interface has
    changed and if users were invoking it, they might experience issues. Selenium Manager is
    still in beta and these type of changes are expected.
- [bidi] Update browsing context create method (#13766)

## 4.19.0

- Add CDP for Chrome 123 and remove 120
- [bidi] Add browser module (#13677)
- [bidi] Add storage module (#13684)
- [bidi] Add fail request command
- [bidi] Add error handling to check BytesValue instance
- [bidi] Add continueRequest and continueResponse command (#13704)
- [bidi] Add provide response command (#13708)
- [bidi] Add setFiles command of the Input Module (#13711)
- [atoms] use css locators in dom.js (#13430)

## 4.18.1

- Add CDP for Chrome 122 and remove 119

## 4.18.0

- Fix running the casting related methods in chromium (#13479)
- [bidi] Add browsing context destroyed event
- [bidi] Add realm destroyed event
- [bidi] Add locate node command (#13489)
- [bidi] Deprecate NetworkInspector in favor of Network
- Make `npm run lint` pass for javascript/selenium-webdriver (#13560)
- [bidi] Add "addintercept" and "removeintercept" commands (#13564)
- [bidi] Add auth related commands (#13572)
- [bidi] Add 'continueWithAuth' command
- [bidi] Add 'fetchError' command

## 4.17.0

- Add javascript to Selenium Manager input for tracking (see #13288)
- remove deprecated headless methods and associated references
- Add script message event (#13153)
- Update channel name from Aurora to Dev
- Remove firefox_channels.js example as Channels is deprecated
- Add Input module command (#13360)
- remove all references to firefox-bin
- download files from remote server (#13102)
- Add auth required event
- Add traverse history command
- Add test to get iframe's browsing context
- Add Input module JS command
- Add test for node properties in
- Add CDP for Chrome 121 and remove 118

## 4.16.0

#### :bug: Bug fix

- Fix logging levels in http.js and webdriver.js (#13098)
- Remove unused targets from the JS tree (#13129)

- #### :nail_care: Polish

- Add CDP v120 and remove v117

## 4.15.0

#### :bug: Bug fix

- Replace calls to console.log with managed loggers (#12909)

#### :nail_care: Polish

- Add CDP v119 and remove v116

#### :rocket: New Feature

- Add BiDi captureScreenshot command (#12510)
- Add BiDi browsing context activate command, handle user prompt command and
  reload command
- Add BiDi browsing context commands and events (#13078)

## 4.14.0

#### :nail_care: Polish

- Adding CDP v118 and removing v115

## 4.13.0

#### :nail_care: Polish

- Adding CDP v117 and removing v114
- Added file location to exception message for Selenium Manager

#### :rocket: New Feature

- Allow users to set Selenium Manager path by environment variable (#12752)

## 4.12.0

#### :bug: Bug fix

- Adding browsers when they are present on the host (#12456)
- Create absolute path for browser binary (#12479)
- Fix how browsers and drivers are discovered (#12456)

#### :nail_care: Polish

- Add support for Chrome 116 and remove support for Chrome 113
- Remove browserVersion from options in Selenium Manager (#12641)

## 4.11.1

#### :bug: Bug fix

- Update testing/index.js code snippet to fix function call (#12456)

## v4.11.0

#### :nail_care: Polish

- [BiDi] fix addPreloadScript failing tests (#12182)
- Print debug message once for each browser when selenium manager used
- Add forgotten RelativeBy in check options (#12289)
- SM supports all browsers in Selenium since a while ago
- Using SM to check drivers on the PATH
- Display info about SM activity
- Removing logic to find drivers, delegating to Selenium Manager
- Removing service parameter from getPath
- add support for Chrome 115 and remove support for Chrome 112
- Update webdriver-bootstrap.js (#12276)

#### :rocket: New Feature

- [BiDi] add Network module events (#12197)
- Adding ignore process match for IE Mode across bindings (#12279)
- Add browser output from Selenium Manager to options (#12411)

#### :bug: Bug Fix

- fix SeleniumServer.start() crashes on MacOS with nodejs selenium-webdriver (
  #12158)
- Update by.js: Add forgotten RelativeBy in check options (#12289)

## v4.10.0

#### :nail_care: Polish

- Adding CDP v114 and removing v111

#### :rocket: New Feature

- [Edge] Support webview2 (#11978)
- [BiDi] Add Script module commands to add/remove preloaded scripts (#11847)
- [BiDi] Add printPage command (#12124)
- Add support for proxies with Selenium Manager

## v4.9.2

#### :nail_care: Polish

- Handle rejection of the driver if not found

## v4.9.1

#### :nail_care: Polish

- Add CDP files for v113 and remove v110

## v4.9.0

#### :nail_care: Polish

- Adding CDP v112 and removing v109

#### :bug: Bug Fix

- Fix: return statement in submit() (#11883)
- [grid] Refining the UI configuration to allow sub paths work properly.
- Replace `execSync` with `spawnSync` in `seleniumManager.js` (#11649) (#11873)

#### :rocket: New Feature

- [BiDi] Add Script module commands and types (#11847)
- Selenium Manager get Browser Version from Options classes
- Selenium Manager use binary from Browser Options

## v4.8.2

#### :nail_care: Polish

- Add CDP support for v111 and remove v108
- Using json output with Selenium Manager

#### :bug: Bug Fix

- fix: Using status from response (#11742)

## v4.8.1

#### :rocket: New Feature

- Add script pinning (#11584)

#### :nail_care: Polish

- Add CDP support for v110 and remove v107
- Updating Selenium Manager binaries for 4.8.1 release

#### :bug: Bug Fix

fix: iedriver download with selenium-manager #11579

## v4.8.0

#### :rocket: New Feature

- Add initial BiDi Support (#11395)
- Add window wrappers getSize and setSize
- Add BiDi browser context commands (#11473)
- Add BiDi methods to listen to js logs and any type of logs
- Add BiDi filtering capability to LogInspector (#11495)
- Add comment with name of large JS executions (#11038)

#### :nail_care: Polish

- Add CDP support for v109 and remove v106
- Deprecate setHeadless() in Chrome and Firefox (#11467)

## v4.7.1

#### :nail_care: Polish

- feat/deprecation message for standalone3x (#11422)

#### :bug: Bug Fix

- feat/fix spawn format for SeleniumServer, issue 11405 (#11412)

#### Committers: 1

- Potapov Dmitriy ([@potapovDim](https://github.com/potapovDim))

## v4.7.0

#### :rocket: New Feature

- Add support for Selenium Manager to work with IE Driver

#### :nail_care: Polish

- Adding CDP files for v108 and removing v105
- Improve error handling for Selenium Manager

## v4.6.1

#### :bug: Bug Fix

- Support Node 17 and above (#11262)

#### :nail_care: Polish

- Fix typos (#11258)

## v4.6.0

#### :rocket: New Feature

- [grid] Add ability to use Enter key to confirm (#11178)
- [grid][ui] Add search field for running sessions (#11197)
- Add support for selenium manager (#11189)

#### :bug: Bug Fix

- isPromise() thenable fix (#11048)
- Fix: making methods static (#11182)

#### :nail_care: Polish

- fix a tiny typo in chromium.js (#11073)
- Adding CDP files for v107 and removing v104

## v4.5.0

#### :rocket: New Feature

- Adds 'Select' support package
  - selectByIndex
  - selectByValue
  - selectByVisibleText
  - getAllSelectedOptions
  - getFirstSelectedOption
  - deselectAll
  - deselectByVisibleText
  - deselectByIndex
  - deselectByValue
- Add support for Actions API sendKeys to designated element
- Adds mouse button enum for forward and backward navigation

#### :bug: Bug Fix

- Set min node support to 14.x.x (issue 1 in #10970)
- fix: geckodriver session with node 18.x.x (issue 2 in #10970)
- fix: JS firefox driver crashes on setting a profile (fixed
  with [commit](https://github.com/SeleniumHQ/selenium/commit/fa6deeea6bda1e73317157845772e114bd569b7d))
- fix: "SetExperimental" option is not available in webdriverjs (
  Javascript/Typescript) (#10959)
- fix: Do not allow Select class to select disabled options (#10812)

#### :nail_care: Polish

- Stop sending desiredCapabilities to local end and remote end
- tests: Quit driver instance after each test
- tests: Adds select by multiple(index,value, text) tests
- chore/code style changes, add util (#10975)
- chore/code style changes to common format (#10964)
- destructuring exports
- Moving from static to const to make js-dossier happy
- Removing circular dependency Between webdriver.js and http.js
- fix some typos in code and documentation
- add cdp v105 remove v102
- add cdp v106 remove v103

## v4.4.0

- Add support CDP 104 and remove CDP 101

## v4.3.1

- create new default service for every driver instance in chromium (#10796)
- Updated jsdoc for move action (#10816)

## v4.3.0

- Fix: using fulfillRequest when intercepting (#10764)
- Add support CDP 103 and remove CDP 100
- Remove unused param in input.js & minor code cleanup
- Fixes lint issues and updates npm packages
- feat: Added virtual authenticator (#10663)

## v4.2.0

- Delete old android-driver
- Remove Opera support from JS bindings #10379
- Handle a breaking changes in os.networkInterfaces (#10611)
- Add support to switch frame by id
- Add support to switch frame by name (#10670)
- [cdp] add support Chrome 102 and remove for Chrome 98
- Update implementation for submitting forms

## v4.1.2

- chore/connect to CDP based on ENV selenium address var (#10255)
- Add wheel support to actions
- add support for Chrome 98 and remove support for Chrome 95
- add deleteNetworkConditions for chromium (#10323)
- Add new pointer attributes to actions
- add support Chrome 99 and remove for Chrome 96
- sendKeys error message fixed
- add support Chrome 100 and remove for Chrome 97
- removing onQuit handler for chromedriver (#10499)
- Split String on grapheme pairs in sendKeys command (#10519)
- do not convert tag name to css selector
- docs: use Browser constants for browser name (#10567)

## v4.1.1

- Add support for installing unpacked addons at runtime in Firefox webdriver (
  #10216)
- Enables firefox debugger for devtools test
- Sets correct browserName when set via SELENIUM_BROWSER fixes #10218
- add support for Chrome v97
- Adds new desktop cast command for Chromium #10190 (#10191)
- ignore errors arising from trying to use file detector fixes #6343
- Added RelativeBy class on the main api (#10148)
- Code cleanup and minor improvements
- Implements 'getDomAttribute' to get attribute value as defined by w3c spec and
  removes legacy
  command usages
- Remove legacy JWP support and w3c<boolean> switch (#10095)
- update map/filter clean up to common format (#10094)

## v4.1.0

- add support for handling Shadow DOM elements
- Capture browser console logs when using BiDi onLogEvent api
- add CDP support for v96 and remove v93
- remove useless package, change io public api import to more common approach (
  #10037)
- Fix flaky CDP dom mutation (#10029)
- Fix edge test failures (#10012)
- code cleanup second iteration (#9979)
- Ensure correct serialization of extensions in chromium.Options (#6676)
- Remove explicit id passing for CDP commands
- Attach to page target while creating a CDP connection (#10005)
- Add support for form submit in W3C mode (fixes #9916) (#9936)
- Fixes relative locator 'locateWith' export
- update doc string for CDP Commands (#9929)

## v4.0.0

- Fixes cdp for Chrome and Firefox (#9909)
- Add cdp 95 to python and JS
- Removing unused target for cdpConnection

## v4.0.0-rc-2

- Fix builder test
- Allow builder to set a single arbitrary capability (#9857)
- Restore withCapabilities() to ensure backward compatibility
- Add new websocketUrl capability
- update ruby, python and JS to add support for v94 and remove support for v92
- Add a check for Grid CDP endpoint
- Implement network request interceptions
- Update connections to devtools so it works in Firefox and Chrome

## v4.0.0-rc-1

- Deprecates withCapabilities method and removes tests (#9701)
- expose `withLocator` method at the top level
- Enables Mobile feature for Firefox (Still need to expand this to chromium
  based browsers)

- Add the ability to do Relative Locators with all By types. Fixes #9559
- Add default Opera capabilities. Fixes #9654
- Add support for ChromeDriver `--enable-chrome-logs` (
  #9575) [Author: Mark Stacey]

  The `--enable-chrome-logs` ChromeDriver option can now be enabled using the
  Chromium (or Chrome)
  ServiceBuilder, similarly to the other options provided by ChromeDriver.

- Add the debuggerAddress option to chromium.js (
  #9561) [Author: Brandon Walderman]
- Adds http client options [#9638](Author: Dharin Shah)
- Updating edge.js to extend chromium.driver (fixes #9626)

## v4.0.0-beta.4

- Add windowTypes option support for ChromiumDriver (#7897)
- Allow relativeby with findElement (#9396)

## v4.0.0-beta.3

- Removed support for legacy actions. This will also remove "Bride Mode"
  support, which was
  introduced to translate of action class to legacy API.
- Remove devtools support for v86,v87 and adding support for v89, v90, v91
- make isPromise() return boolean value for null and undefined cases (#6785)
- code cleanup (#9158)
- replacing asserts equal with strictEqual and deepEqual with deepStrictEqual

## v4.0.0-beta.2

- Removed support for Edge legacy.
- Make the build silent for WebDriver Bidi generated code
- resolve file path correctly (#9223)
- Update example in edge.js (#9231)
- Use a flat namespace for selenium options for finding WebDriver Bidi endpoint
- Removing duplicate license text updated by update_copyright.js
- Updating copyright text
- Minor code cleanup
- Removing duplicate variable declaration of IE from capabilities

## v4.0.0-beta.1

- JS Binding support for WebDriver Bidi in Firefox
  - This requires Firefox 87 and Geckodriver 0.29 to be able to work
- Update the supported CDP versions
- Update tmp package version (#9155)
- Adding support for /computedlabel (getAccessibleName) (#9073)
- Adding support for computedrole (#8990)
- Use subfolder for NPM package homepage (#8938)
- Fix typo: tracingCategories -> traceCategories (#8976)
- Package up mutation-listener for node. Fixes #8955
- Adding support for `print` endpoint (#8880)

## v4.0.0-alpha.8

- Rolling back native support for Opera Browser
- Added new ieOptions capabilities:

  - fileUploadDialogTimeout
  - setEdgePath
  - setEdgeChromium
  - setScrollBehavior
  - For consistent naming, deprecating `addArguments(...args)` in favor
    of `addBrowserCommandSwitches(...args)`

- Added relative locators
- Added Chrome DevTools Protocol (CDP) support
- Added support for BASIC authentication.
- Added listener for DOM mutations.
- Added support for listening out for console log events.
- Added support listening js exceptions using CDP

### API Changes

- Added the 'opera' module

## v4.0.0-alpha.4

### Changes

- Removed BUILD.bazel files from the build artifact

## v4.0.0-alpha.3

### Notice

- The minimum supported version of Node is now 10.15.0 LTS

### Changes

- add `pollTimeout` argument to the `wait()` method. Default value is `200`ms
- add `switchTo().parentFrame()` for non-W3C compatible drivers
- add support for opening a new window

### API Changes

- Export `lib/input.Origin` from the top level `selenium-webdriver` module.
- HTTP requests from this library will now include a User-Agent of the form
  `selenium/${VERSION} (js ${PLATFORM})`.

## v4.0.0-alpha.1

### Notice

- The minimum supported version of Node is now 8.9.0 LTS

### Changes to Supported Browsers

Native support has been removed for Opera and PhantomJS as the WebDriver
implementations for these browsers are no longer under active development.

For Opera, users should be able to simply rely on testing Chrome as the Opera
browser is based on Chromium (and the operadriver was a thin wrapper around
chromedriver). For PhantomJS, users should use Chrome or Firefox in headless
mode (see `example/headless.js`)

### Changes for W3C WebDriver Spec Compliance

- Revamped the actions API to conform with the WebDriver Spec:
  <https://www.w3.org/TR/webdriver/#actions>. For details, refer to the JS doc
  on
  the `lib/input.Actions` class.

  As of January, 2018, only Firefox natively supports this new API. You can put
  the `Actions` class
  into "bridge mode" and it will attempt to translate mouse and keyboard actions
  to the legacy API (
  see class docs). Alternatively, you may continue to use the legacy API
  directly via
  the `lib/actions` module.
  **NOTE:** The legacy API is considered strongly deprecated and will be removed
  in a minor release
  once Google's Chrome and Microsoft's Edge browsers support the new API.

- All window manipulation commands are now supported.
- Added `driver.switchTo().parentFrame()`
- When a named cookie is requested, attempt to fetch it directly using the W3C
  endpoint, `GET /session/{session id}/cookie/{name}`. If this command is not
  recognized by the
  remote end, fallback to fetching all cookies and then searching for the
  desired name.
- Replaced `WebElement.getSize()` and `WebElement.getLocation()` with a single
  method, `WebElement.getRect()`.

### API Changes

- The core WebDriver API no longer uses promise manager
  - Removed `index.Builder#setControlFlow()`
  - The following thenable types no longer have a `cancel()` method:
  - The dynamically generated thenable WebDrivers created by `index.Builder`
  - `lib/webdriver.AlertPromise`
  - `lib/webdriver.WebElementPromise`
- Removed `remote/index.DriverService.prototype.stop()` (use `#kill()` instead)
- Removed the `lib/actions` module
- Removed the `lib/events` module
- Removed the `phantomjs` module
- Removed the 'opera' module
- Removed the promise manager from `lib/promise`, which includes the removal of
  the following
  exported names (replacements, if any, in parentheses):
  - CancellableThenable
  - CancellationError
  - ControlFlow
  - Deferred
  - LONG_STACK_TRACES
  - MultipleUnhandledRejectionError
  - Promise (use native Promises)
  - Resolver
  - Scheduler
  - Thenable
  - USE_PROMISE_MANAGER
  - all (use Promise.all)
  - asap (use Promise.resolve)
  - captureStackTrace (use Error.captureStackTrace)
  - consume (use async functions)
  - controlFlow
  - createPromise (use new Promise)
  - defer
  - fulfilled (use Promise.resolve)
  - isGenerator
  - rejected (use Promise.reject)
  - setDefaultFlow
  - when (use Promise.resolve)
- Changes to the `Builder` class:
  - Added setChromeService, setEdgeService, & setFirefoxService
  - Removed setEnableNativeEvents
  - Removed setScrollBehavior
- Changes to `chrome.Driver`
  - Added sendDevToolsCommand
  - Added setDownloadPath
- Changes to `chrome.Options`
  - Now extends the `Capabilities` class
  - Removed from/toCapabilities
- Changes to `edge.Options`
  - Now extends the `Capabilities` class
  - Removed from/toCapabilities
- Changes to `ie.Options`
  - Now extends the `Capabilities` class
  - Removed from/toCapabilities
- Removed the `firefox.Binary` class. Custom binaries can still be selected
  using `firefox.Options#setBinary()`. Likewise, custom binary arguments can be
  specified
  with `firefox.Options#addArguments()`.
- Changes to `firefox.Driver`
  - Added installAddon(path)
  - Added uninstallAddon(id)
- Changes to `firefox.Options`
  - Now extends the `Capabilities` class
  - Removed from/toCapabilities
  - Removed setLoggingPreferences (was a no-op)
  - setProfile now only accepts a path to an existing profile
  - Added addExtensions
  - Added setPreference
- Removed the `firefox.Profile` class. All of its functionality is now provided
  directly
  by `firefox.Options`
- Removed the `firefox/binary` module
- Removed the `firefox/profile` module
- Changes to `safari.Options`
  - Now extends the `Capabilities` class
  - Removed from/toCapabilities
  - Removed setCleanSession (was a no-op)
- Changes to `lib/capabilities.Browser`:
  - Removed several enum values.
    - ANDROID (use Chrome for Android; see docs on the chrome module)
    - IPAD (no support available)
    - IPHONE (no support available)
    - OPERA (use Chrome)
    - PHANTOM_JS (use Chrome or Firefox in headless mode)
    - HTMLUNIT (use Chrome or Firefox in headless mode)
- Changes to `lib/capabilities.Capabilities`:
  - Removed static factory methods android(), ipad(), iphone(), opera(),
    phantomjs(), htmlunit(),
    and htmlunitwithjs(). Users can still manually configure capabilities for
    these, but their use
    is not recommended and they will no longer be surfaced in the API.
- Changes to `lib/error`:
  - Added
    - ElementClickInterceptedError
    - InsecureCertificateError
    - InvalidCoordinatesError
    - NoSuchCookieError
  - Removed
    - ElementNotVisibleError
    - InvalidElementCoordinatesError
- Changes to `lib/webdriver.WebDriver`:
  - Dropped support for "requiredCapabilities" from WebDriver.createSession
  - actions() now returns the new `lib/input.Actions` class
  - Removed touchActions
  - Renamed schedule to execute
  - Removed the `WebDriver.attachToSession()` factory method. Users can just
    the `WebDriver`
    constructor directly instead.
  - Removed the `call()` method. This was used to inject custom function calls
    into the control
    flow. Now that the promise manager is no longer used, this method is no
    longer necessary.
    Users are now responsible for coordinating actions (ideally with async
    functions) and can just
    call functions directly instead of through `driver.call()`.
- Changes to `lib/webdriver.WebElement`:
  - Replaced getSize & getLocation with getRect
- Changes to `lib/webdriver.Alert`:
  - Removed authenticateAs
- Changes to `lib/webdriver.Options` (`driver.manage()`):
  - Removed timeouts (use get/setTimeouts)
- Changes to `lib/webdriver.Window` (`driver.manage().window()`):
  - Added
    - getRect
    - setRect
    - fullscreen
    - minimize
  - Removed (use the getRect/setRect methods)
    - getPosition
    - setPosition
    - getSize
    - setSize
- Removed the `testing/assert` module
- Changes to `testing/index`
  - Since the promise manager has been removed, it is no longer necessary to
    wrap the Mocha test
    hooks; instead, users can simply use async functions. The following have all
    been removed:
    - describe
    - before
    - beforeEach
    - after
    - afterEach
    - it
  - Added the `suite` function. For details, refer to the jsdoc or
    `example/google_search_test.js`

## v3.6.0

### Bug Fixes

- The Capabilities factory methods should only specify the name of the browser.
- Protect against the remote end sometimes not returning a list to findElements
  commands.
- Properly reset state in `remote.DriverService#kill()`
- The firefox module will no longer apply the preferences required by the legacy
  FirefoxDriver.
  These preferences were only required when using the legacy driver, support for
  which was dropped
  in v3.5.0.

### API Changes

- Added new methods to `selenium-webdriver/firefox.Options`:
  - addArguments()
  - headless()
  - windowSize()
- Deprecated `selenium-webdriver/firefox/binary.Binary`
- Removed `selenium-webdriver/firefox.Options#useGeckoDriver()`
- Removed the unused `selenium-webdriver/firefox/profile.decode()`
- Removed methods from `selenium-webdriver/firefox/profile.Profile` that had no
  effect since support
  for the legacy FirefoxDriver was dropped in 3.5.0:
  - setNativeEventsEnabled
  - nativeEventsEnabled
  - getPort
  - setPort
- Removed `selenium-webdriver/firefox.ServiceBuilder#setFirefoxBinary()`; custom
  binaries should be
  configured through the `firefox.Options` class.
- Removed `selenium-webdriver/firefox.Capability`. These hold overs from the
  legacy FirefoxDriver
  are no longer supported.

### Changes for W3C WebDriver Spec Compliance

- Deprecated `error.ElementNotVisibleError` in favor of the more generic
  `error.ElementNotInteractableError`.
- Support the `httpOnly` option when adding a cookie.

## v3.5.0

### Notice

Native support for Firefox 45 (ESR) has been removed. Users will have to connect
to a remote Selenium server that supports Firefox 45.

### Changes

- Removed native support for Firefox 46 and older.
  - The `SELENIUM_MARIONETTE` enviornment variable no longer has an effect.
  - `selenium-webdriver/firefox.Capability.MARIONETTE` is deprecated.
  - `selenium-webdriver/firefox.Options#useGeckoDriver()` is deprecated and now
    a no-op.
- `firefox.Options` will no longer discard the `"moz:firefoxOptions"` set in
  user provided
  capabilities (via `Builder.withCapabilities({})`). When both are used, the
  settings
  in `firefox.Options` will be applied _last_.
- Added `chrome.Options#headless()` and `chrome.Options#windowSize()`, which may
  be used to start
  Chrome in headless mode (requires Chrome 59+) and to set the initial window
  size, respectively.

### Changes for W3C WebDriver Spec Compliance

- Added `error.WebDriverError#remoteStacktrace` to capture the stacktrace
  reported by a remote
  WebDriver endpoint (if any).
- Fixed `WebElement#sendKeys` to send text as a string instead of an array of
  strings.

## v3.4.0

### Notice

This release
requires [geckodriver 0.15.0](https://github.com/mozilla/geckodriver/releases/tag/v0.15.0)
or newer.

### API Changes

- Added `Options#getTimeouts()` for retrieving the currently configured session
  timeouts (i.e.
  implicit wait). This method will only work with W3C compatible WebDriver
  implementations.
- Deprecated the `Timeouts` class in favor of `Options#setTimeouts()`, which
  supports setting
  multiple timeouts at once.
- Added support for emulating different network conditions (e.g., offline, 2G,
  WiFi) on Chrome.

### Changes for W3C WebDriver Spec Compliance

- Fixed W3C response parsing, which expects response data to always be a JSON
  object with a `value`
  key.
- Added W3C endpoints for interacting with various types of
  [user prompts](https://w3c.github.io/webdriver/webdriver-spec.html#user-prompts).
- Added W3C endpoints for remotely executing scripts.
- Added W3C endpoints to get current window handle and all windows handles.

## v3.3.0

- Added warning log messages when the user creates new managed promises, or
  schedules unchained
  tasks. Users may opt in to printing these log messages with

  ```js
  const { logging } = require('selenium-webdriver')
  logging.installConsoleHandler()
  logging.getLogger('promise.ControlFlow').setLevel(logging.Level.WARNING)
  ```

- If the `JAVA_HOME` environment variable is set, use it to locate java.exe.

## v3.2.0

- Release skipped to stay in sync with the main Selenium project.

## v3.1.0

- The `lib` package is once again platform agnostic (excluding `lib/devmode`).
- Deprecated `promise.when(value, callback, errback)`.
  Use `promise.fulfilled(value).then(callback, errback)`
- Changed `promise.fulfilled(value)`, `promise.rejected(reason)` and
  `promise.defer()` to all use native promises when the promise manager is
  disabled.
- Properly handle W3C error responses to new session commands.
- Updated `selenium-webdriver/testing` to export `describe.only` along with
  `describe.skip`.
- Fixed `selenium-webdriver/lib/until.ableToSwitchToFrame`. It was previously
  dropping arguments and
  would never work.
- Added the ability to use Firefox Nightly
- If Firefox cannot be found in the default location, look for it on the PATH
- Allow SafariDriver to use Safari Technology Preview.
- Use the proper wire command for WebElement.getLocation() and
  WebElement.getSize() for W3C
  compliant drivers.

## v3.0.1

- More API adjustments to align with native Promises
  - Deprecated `promise.fulfilled(value)`, use `promise.Promise#resolve(value)`
  - Deprecated `promise.rejected(reason)`, use `promise.Promise#reject(reason)`
- When a `wait()` condition times out, the returned promise will now be rejected
  with
  an `error.TimeoutError` instead of a generic `Error` object.
- `WebDriver#wait()` will now throw a TypeError if an invalid wait condition is
  provided.
- Properly catch unhandled promise rejections with an action sequence (only
  impacts when the promise
  manager is disabled).

## v3.0.0

- (**NOTICE**) The minimum supported version of Node is now 6.9.0 LTS
- Removed support for the SafariDriver browser extension. This has been replaced
  by Apple's
  safaridriver, which is included wtih Safari 10
  (available on OS X El Capitan and macOS Sierra).

  To use Safari 9 or older, users will have to use an older version of Selenium.

- geckodriver v0.11.0 or newer is now required for Firefox.
- Fixed potential reference errors in `selenium-webdriver/testing` when users
  create a cycle with
  mocha by running with mocha's `--hook` flag.
- Fixed `WebDriver.switchTo().activeElement()` to use the correct HTTP method
  for compatibility with
  the W3C spec.
- Update the `selenium-webdriver/firefox` module to use geckodriver's
  "moz:firefoxOptions" dictionary for Firefox-specific configuration values.
- Extending the `selenium-webdriver/testing` module to support tests defined
  using generator
  functions.
- The promise manager can be disabled by setting an enviornment variable:
  `SELENIUM_PROMISE_MANAGER=0`. This is part of a larger plan to remove the
  promise manager, as
  documented at
  <https://github.com/SeleniumHQ/selenium/issues/2969>
- When communicating with a W3C-compliant remote end, use the atoms library for
  the `WebElement.getAttribute()` and `WebElement.isDisplayed()` commands. This
  behavior is
  consistent with the java, .net, python, and ruby clients.

### API Changes

- Removed `safari.Options#useLegacyDriver()`
- Reduced the API on `promise.Thenable` for compatibility with native promises:
  - Removed `#isPending()`
  - Removed `#cancel()`
  - Removed `#finally()`
- Changed all subclasses of `webdriver.WebDriver` to overload the static
  function `WebDriver.createSession()` instead of doing work in the constructor.
  All constructors
  now inherit the base class' function signature. Users are still encouraged to
  use the `Builder`
  class instead of creating drivers directly.
- `Builder#build()` now returns a "thenable" WebDriver instance, allowing users
  to immediately
  schedule commands (as before), or issue them through standard promise
  callbacks. This is the same
  pattern already employed for WebElements.
- Removed `Builder#buildAsync()` as it was redundant with the new semantics of
  `build()`.

## v3.0.0-beta-3

- Fixed a bug where the promise manager would silently drop callbacks after
  recovering from an
  unhandled promise rejection.
- Added the `firefox.ServiceBuilder` class, which may be used to customize the
  geckodriver used
  for `firefox.Driver` instances.
- Added support for Safari 10 safaridriver. safaridriver may be disabled via tha
  API, `safari.Options#useLegacyDriver`, to use the safari extension driver.
- Updated the `lib/proxy` module to support configuring a SOCKS proxy.
- For the `promise.ControlFlow`, fire the "uncaughtException" event in a new
  turn of the JS event
  loop. As a result of this change, any errors thrown by an event listener will
  propagate to the
  global error handler. Previously, this event was fired with in the context of
  a (native) promise
  callback, causing errors to be silently suppressed in the promise chain.

### API Changes

- Added `remote.DriverService.Builder` as a base class for configuring
  DriverService instances that
  run in a child-process. The
  `chrome.ServiceBuilder`, `edge.ServiceBuilder`, and `opera.ServiceBuilder`
  classes now all extend this base class with browser-specific options.
- For each of the ServiceBuilder classes, renamed `usingPort` and
  `withEnvironment` to `setPort` and `setEnvironment`, respectively.
- Renamed `chrome.ServiceBuilder#setUrlBasePath` to `#setPath`
- Changed the signature of the `firefox.Driver` from `(config, flow, executor)`
  to `(config, executor, flow)`.
- Exposed the `Condition` and `WebElementCondition` classes from the top-level
  `selenium-webdriver` module (these were previously only available from
  `lib/webdriver`).

### Changes for W3C WebDriver Spec Compliance

- Updated command mappings
  for [getting](https://w3c.github.io/webdriver/webdriver-spec.html#get-window-position)
  and [setting](https://w3c.github.io/webdriver/webdriver-spec.html#set-window-position)
  the window position.

## v3.0.0-beta-2

### API Changes

- Moved the `builder.Builder` class into the main module (`selenium-webdriver`).
- Removed the `builder` module.
- Fix `webdriver.WebDriver#setFileDetector` when driving Chrome or Firefox on a
  remote machine.

## v3.0.0-beta-1

- Allow users to set the agent used for HTTP connections through
  `builder.Builder#usingHttpAgent()`
- Added new wait conditions: `until.urlIs()`, `until.urlContains()`,
  `until.urlMatches()`
- Added work around
  for [GeckoDriver bug](https://bugzilla.mozilla.org/show_bug.cgi?id=1274924)
  raising a type conversion error
- Internal cleanup replacing uses of managed promises with native promises
- Removed the mandatory use of Firefox Dev Edition, when using Marionette driver
- Fixed timeouts' URL
- Properly send HTTP requests when using a WebDriver server proxy
- Properly configure proxies when using the geckodriver
- `http.Executor` now accepts a promised client. The `builder.Builder` class
  will now use this
  instead of a `command.DeferredExecutor` when creating WebDriver instances.
- For Chrome and Firefox, the `builder.Builder` class will always return an
  instanceof `chrome.Driver` and `firefox.Driver`, respectively, even when
  configured to use a
  remote server (from `builder.Builder#usingServer(url)`,
  `SELENIUM_REMOTE_URL`, etc).

### API Changes

- `promise.Deferred` is no longer a thenable object.
- `Options#addCookie()` now takes a record object instead of 7 individual
  parameters. A TypeError
  will be thrown if addCookie() is called with invalid arguments.
- When adding cookies, the desired expiry must be provided as a Date or in
  _seconds_ since epoch. When retrieving cookies, the expiration is always
  returned in seconds.
- Renamed `firefox.Options#useMarionette` to `firefox.Options#useGeckoDriver`
- Removed deprecated modules:
  - `selenium-webdriver/error` (use `selenium-webdriver/lib/error`,\
    or the `error` property exported by `selenium-webdriver`)
  - `selenium-webdriver/executors` — this was not previously deprecated, but is
    no longer used.
- Removed deprecated types:
  - `command.DeferredExecutor` — this was not previously deprecated, but is no
    longer used. It can
    be trivially implemented by clients should it be needed.
  - `error.InvalidSessionIdError` (use `error.NoSuchSessionError`)
  - `executors.DeferredExecutor`
  - `until.Condition` (use `webdriver.Condition`)
  - `until.WebElementCondition` (use `webdriver.WebElementCondition`)
  - `webdriver.UnhandledAlertError` (use `error.UnexpectedAlertOpenError`)
- Removed deprecated functions:
  - `Deferred#cancel()`
  - `Deferred#catch()`
  - `Deferred#finally()`
  - `Deferred#isPending()`
  - `Deferred#then()`
  - `Promise#thenCatch()`
  - `Promise#thenFinally()`
  - `WebDriver#isElementPresent()`
  - `WebElement#getInnerHtml()`
  - `WebElement#getOuterHtml()`
  - `WebElement#getRawId()`
  - `WebElement#isElementPresent()`
- Removed deprecated properties:
  - `WebDriverError#code`

## v2.53.2

- Changed `io.exists()` to return a rejected promise if the input path is not a
  string
- Deprecated `Promise#thenFinally()` - use `Promise#finally()`. The thenFinally
  shim added to the
  promise module in v2.53.0 will be removed in v3.0 Sorry for the churn!
- FIXED: capabilities serialization now properly handles undefined vs.
  false-like values.
- FIXED: properly handle responses from the remote end in
  `WebDriver.attachToSession`

## v2.53.1

- FIXED: for consistency with the other language bindings, `remote.FileDetector`
  will ignore paths that refer to a directory.

## v2.53.0

### Change Summary

- Added preliminary support for Marionette, Mozilla's WebDriver implementation
  for Firefox.
  Marionette may be enabled via the API,
  `firefox.Options#useMarionette`, or by setting the `SELENIUM_MARIONETTE`
  environment variable.
- Moved all logic for parsing and interpreting responses from the remote end
  into the
  individual `command.Executor` implementations.
- For consistency with the other Selenium language bindings,
  `WebDriver#isElementPresent()` and `WebElement#isElementPresent()` have been
  deprecated. These
  methods will be removed in v3.0. Use the findElements command to test for the
  presence of an
  element:

      driver.findElements(By.css('.foo')).then(found => !!found.length);

- Added support for W3C-spec compliant servers.
- For consistent naming, deprecating `error.InvalidSessionIdError` in favor of
  `error.NoSuchSessionError`.
- Moved the `error` module to `lib/error` so all core modules are co-located.
  The top-level `error`
  module will be removed in v3.0.
- Moved `until.Condition` and `until.WebElementCondition` to the webdriver
  module to break a
  circular dependency.
- Added support for setting the username and password in basic auth pop-up
  dialogs (currently IE
  only).
- Deprecated `WebElement#getInnerHtml()` and `WebEleemnt#getOuterHtml()`
- Deprecated `Promise#thenCatch()` - use `Promise#catch()` instead
- Deprecated `Promise#thenFinally()` - use `promise.thenFinally()` instead
- FIXED: `io.findInPath()` will no longer match against directories that have
  the same basename as
  the target file.
- FIXED: `phantomjs.Driver` now takes a third argument that defines the path to
  a log file to use
  for the phantomjs executable's output. This may be quickly set at runtime with
  the `SELENIUM_PHANTOMJS_LOG` environment variable.

### Changes for W3C WebDriver Spec Compliance

- Changed `element.sendKeys(...)` to send the key sequence as an array where
  each element defines a
  single key. The legacy wire protocol permits arrays where each element is a
  string of arbitrary
  length. This change is solely at the protocol level and should have no
  user-visible effect.

## v2.52.0

### Notice

Starting with v2.52.0, each release of selenium-webdriver will support the
latest _minor_ LTS and stable Node releases. All releases between the LTS and
stable release will have best effort support. Further details are available in
the selenium-webdriver package README.

### Change Summary

- Add support for Microsoft's Edge web browser
- Added `webdriver.Builder#buildAsync()`, which returns a promise that will be
  fulfilled with the
  newly created WebDriver instance once the associated browser has been full
  initialized. This is
  purely a convenient alternative to the existing build() method as the
  WebDriver class will always
  defer commands until it has a fully created browser.
- Added `firefox.Profile#setHost()` which may be used to set the host that the
  FirefoxDriver's
  server listens for commands on. The server uses
  "localhost" by default.
- Added `promise.Promise#catch()` for API compatibility with native Promises.
  `promise.Promise#thenCatch()` is not yet deprecated, but it simply delegates
  to `catch`.
- Changed some `io` operations to use native promises.
- Changed `command.Executor#execute()` and `HttpClient#send()` to return
  promises instead of using
  callback passing.
- Replaced the `Serializable` class with an internal, Symbol-defined method.
- Changed the `Capabilities` class to extend the native `Map` type.
- Changed the `Capabilities.has(key)` to only test if a capability has been set
  (Map semantics). To check whether the value is true, use `get(key)`.
- Deprecated `executors.DeferredExecutor` in favor of
  `lib/command.DeferredExecutor`.
- API documentation is no longer distributed with the npm package, but remains
  available
  at <http://seleniumhq.github.io/selenium/docs/api/javascript/>
- Rewrote the `error` module to export an Error subtype for each type of error
  defined in
  the [W3C WebDriver spec](https://w3c.github.io/webdriver/webdriver-spec.html#handling-errors).
- Changed the `http.Request` and `http.Response` classes to store headers in
  maps instead of object
  literals.
- Updated `ws` dependency to version `1.0.1`.
- Removed fluent predicates "is" and "not" from the experimental
  `testing/assert` module.
- Wait conditions that locate an element, or that wait on an element's state,
  will return a
  WebElementPromise.
- Lots of internal clean-up to break selenium-webdriver's long standing
  dependency on Google's
  Closure library.

### Changes for W3C WebDriver Spec Compliance

- Updated the `By` locators that are not in the W3C spec to delegated to using
  CSS
  selectors: `By.className`, `By.id`, `By.name`, and `By.tagName`.

## v2.49-51

- _Releases skipped to stay in sync with the rest of the Selenium project_

## v2.48.2

- Added `WebElement#takeScreenshot()`.
- More adjustments to promise callback tracking.

## v2.48.1

- FIXED: Adjusted how the control flow tracks promise callbacks to avoid a
  potential deadlock.

## v2.48.0

- Node v0.12.x users must run with --harmony. _This is the last release that
  will support v0.12.x_
- FIXED: (Promise/A+ compliance) When a promise is rejected with a thenable, the
  promise adopts the
  thenable as its rejection reason instead of waiting for it to settle. The
  previous (incorrect)
  behavior was hidden by bugs in the `promises-aplus-tests` compliance test
  suite that were fixed in
  version
  `2.1.1`.
- FIXED: the `webdriver.promise.ControlFlow` now has a consistent execution
  order for
  tasks/callbacks scheduled in different turns of the JS event loop. Refer to
  the `webdriver.promise` documentation for more details.
- FIXED: do not drop user auth from the WebDriver server URL.
- FIXED: a single `firefox.Binary` instance may be used to configure and launch
  multiple
  FirefoxDriver sessions.

      var binary = new firefox.Binary();
      var options = new firefox.Options().setBinary(binary);
      var builder = new Builder().setFirefoxOptions(options);

      var driver1 = builder.build();
      var driver2 = builder.build();

- FIXED: zip files created for transfer to a remote WebDriver server are no
  longer compressed. If
  the zip contained a file that was already compressed, the server would return
  an "invalid code
  lengths set" error.
- FIXED: Surfaced the `loopback` option to `remote/SeleniumServer`. When set,
  the server will be
  accessed using the current host's loopback address.

## v2.47.0

### Notice

This is the last release for `selenium-webdriver` that will support ES5.
Subsequent releases will depend on ES6 features that are enabled by
[default](https://nodejs.org/en/docs/es6/) in Node v4.0.0. Node v0.12.x will
continue to be supported, but will require setting the `--harmony` flag.

### Change Summary

- Add support for [Node v4.0.0](https://nodejs.org/en/blog/release/v4.0.0/)
  - Updated `ws` dependency from `0.7.1` to `0.8.0`
- Bumped the minimum supported version of Node from `0.10.x` to `0.12.x`. This
  is in accordance with
  the Node support policy established in `v2.45.0`.

## v2.46.1

- Fixed internal module loading on Windows.
- Fixed error message format on timeouts for `until.elementLocated()`
  and `until.elementsLocated()`.

## v2.46.0

- Exposed a new logging API via the `webdriver.logging` module. For usage, see
  `example/logging.js`.
- Added support for using a proxy server for WebDriver commands.
  See `Builder#usingWebDriverProxy()`
  for more info.
- Removed deprecated functions:
  - Capabilities#toJSON()
  - UnhandledAlertError#getAlert()
  - chrome.createDriver()
  - phantomjs.createDriver()
  - promise.ControlFlow#annotateError()
  - promise.ControlFlow#await()
  - promise.ControlFlow#clearHistory()
  - promise.ControlFlow#getHistory()
- Removed deprecated enum values: `ErrorCode.NO_MODAL_DIALOG_OPEN` and
  `ErrorCode.MODAL_DIALOG_OPENED`. Use `ErrorCode.NO_SUCH_ALERT` and
  `ErrorCode.UNEXPECTED_ALERT_OPEN`, respectively.
- FIXED: The `promise.ControlFlow` will maintain state for promise chains
  generated in a loop.
- FIXED: Correct serialize target elements used in an action sequence.
- FIXED: `promise.ControlFlow#wait()` now has consistent semantics for an
  omitted or 0-timeout: it
  will wait indefinitely.
- FIXED: `remote.DriverService#start()` will now fail if the child process dies
  while waiting for
  the server to start accepting requests. Previously, start would continue to
  poll the server
  address until the timeout expired.
- FIXED: Skip launching Firefox with the `-silent` flag to preheat the profile.
  Starting with
  Firefox 38, this would cause the browser to crash. This step, which was first
  introduced for
  Selenium's java client back with Firefox 2, no longer appears to be required.
- FIXED: 8564: `firefox.Driver#quit()` will wait for the Firefox process to
  terminate before
  deleting the temporary webdriver profile. This eliminates a race condition
  where Firefox would
  write profile data during shutdown, causing the `rm -rf` operation on the
  profile directory to
  fail.

## v2.45.1

- FIXED: 8548: Task callbacks are once again dropped if the task was cancelled
  due to a previously
  uncaught error within the frame.
- FIXED: 8496: Extended the `chrome.Options` API to cover all configuration
  options (e.g. mobile
  emulation and performance logging) documented on the
  ChromeDriver [project site](https://chromedriver.chromium.org/capabilities).

## v2.45.0

### Important Policy Change

Starting with the 2.45.0 release, selenium-webdriver will support the last
two stable minor releases for Node. For 2.45.0, this means Selenium will
support Node 0.10.x and 0.12.x. Support for the intermediate, un-stable release
(0.11.x) is "best-effort". This policy will be re-evaluated once Node has a
major version release (i.e. 1.0.0).

### Change Summary

- Added native browser support for Internet Explorer, Opera 26+, and Safari
- With the release
  of [Node 0.12.0](http://blog.nodejs.org/2015/02/06/node-v0-12-0-stable/)
  (finally!), the minimum supported version of Node is now `0.10.x`.
- The `promise` module is now [Promises/A+](https://promisesaplus.com/)
  compliant. The biggest compliance change is that promise callbacks are now
  invoked in a future
  turn of the JS event loop. For example:

        var promise = require('selenium-webdriver').promise;
        console.log('start');
        promise.fulfilled().then(function() {
          console.log('middle');
        });
        console.log('end');

        // Output in selenium-webdriver@2.44.0
        // start
        // middle
        // end
        //
        // Output in selenium-webdriver@2.45.0
        // start
        // end
        // middle

  The `promise.ControlFlow` class has been updated to track the asynchronous
  breaks required by
  Promises/A+, so there are no changes to task execution order.

- Updated how errors are annotated on failures. When a task fails, the
  stacktrace from when that
  task was scheduled is appended to the rejection reason with a `From:` prefix (
  if it is an Error
  object). For example:

        var driver = new webdriver.Builder().forBrowser('chrome').build();
        driver.get('http://www.google.com/ncr');
        driver.call(function() {
          driver.wait(function() {
            return driver.isElementPresent(webdriver.By.id('not-there'));
          }, 2000, 'element not found');
        });

  This code will fail an error like:

        Error: element not found
        Wait timed out after 2002ms
            at <stack trace>
        From: Task: element not found
            at <stack trace>
        From: Task: WebDriver.call(function)
            at <stack trace>

- Changed the format of strings returned by `promise.ControlFlow#getSchedule`.
  This function now
  accepts a boolean to control whether the returned string should include the
  stacktraces for when
  each task was scheduled.
- Deprecating `promise.ControlFlow#getHistory`,
  `promise.ControlFlow#clearHistory`, and `promise.ControlFlow#annotateError`.
  These functions were
  all intended for internal use and are no longer necessary, so they have been
  made no-ops.
- `WebDriver.wait()` may now be used to wait for a promise to resolve, with an
  optional timeout.
  Refer to the API documentation for more information.
- Added support for copying files to a remote Selenium via `sendKeys` to test
  file uploads. Refer to
  the API documentation for more information. Sample usage included
  in `test/upload_test.js`
- Expanded the interactions API to include touch actions.
  See `WebDriver.touchActions()`.
- FIXED: 8380: `firefox.Driver` will delete its temporary profile on `quit`.
- FIXED: 8306: Stack overflow in promise callbacks eliminated.
- FIXED: 8221: Added support for defining custom command mappings. Includes
  support for
  PhantomJS's `executePhantomJS` (requires PhantomJS 1.9.7 or GhostDriver
  1.1.0).
- FIXED: 8128: When the FirefoxDriver marshals an object to the page for
  `executeScript`, it defines additional properties (required by the driver's
  implementation). These
  properties will no longer be enumerable and should be omitted (i.e. they won't
  show up in
  JSON.stringify output).
- FIXED: 8094: The control flow will no longer deadlock when a task returns a
  promise that depends
  on the completion of sub-tasks.

## v2.44.0

- Added the `until` module, which defines common explicit wait conditions.
  Sample usage:

        var firefox = require('selenium-webdriver/firefox'),
            until = require('selenium-webdriver/until');

        var driver = new firefox.Driver();
        driver.get('http://www.google.com/ncr');
        driver.wait(until.titleIs('Google Search'), 1000);

- FIXED: 8000: `Builder.forBrowser()` now accepts an empty string since some
  WebDriver
  implementations ignore the value. A value must still be specified, however,
  since it is a required
  field in WebDriver's wire protocol.
- FIXED: 7994: The `stacktrace` module will not modify stack traces if the
  initial parse fails (e.g.
  the user defined `Error.prepareStackTrace`)
- FIXED: 5855: Added a module (`until`) that defines several common conditions
  for use with explicit
  waits. See updated examples for usage.

## v2.43.5

- FIXED: 7905: `Builder.usingServer(url)` once again returns `this` for
  chaining.

## v2.43.2-4

- No changes; version bumps while attempting to work around an issue with
  publishing to npm (a
  version string may only be used once).

## v2.43.1

- Fixed an issue with flakiness when setting up the Firefox profile that could
  prevent the driver
  from initializing properly.

## v2.43.0

- Added native support for Firefox - the Java Selenium server is no longer
  required.
- Added support for generator functions to `ControlFlow#execute` and
  `ControlFlow#wait`. For more information, see documentation on
  `webdriver.promise.consume`. Requires harmony support (run with
  `node --harmony-generators` in `v0.11.x`).
- Various improvements to the `Builder` API. Notably, the `build()` function
  will no longer default
  to attempting to use a server at
  `http://localhost:4444/wd/hub` if it cannot start a browser directly - you
  must specify the
  WebDriver server with `usingServer(url)`. You can also set the target browser
  and WebDriver server
  through a pair of environment variables. See the documentation on
  the `Builder` constructor for
  more information.
- For consistency with the other language bindings, added browser specific
  classes that can be used
  to start a browser without the builder.

        var webdriver = require('selenium-webdriver')
            chrome = require('selenium-webdriver/chrome');

        // The following are equivalent.
        var driver1 = new webdriver.Builder().forBrowser('chrome').build();
        var driver2 = new chrome.Driver();

- Promise A+ compliance: a promise may no longer resolve to itself.
- For consistency with other language bindings, deprecated
  `UnhandledAlertError#getAlert` and added `#getAlertText`.
  `getAlert` will be removed in `2.45.0`.
- FIXED: 7641: Deprecated `ErrorCode.NO_MODAL_DIALOG_OPEN` and
  `ErrorCode.MODAL_DIALOG_OPENED` in favor of the new
  `ErrorCode.NO_SUCH_ALERT` and `ErrorCode.UNEXPECTED_ALERT_OPEN`, respectively.
- FIXED: 7563: Mocha integration no longer disables timeouts. Default Mocha
  timeouts apply (2000 ms)
  and may be changed using `this.timeout(ms)`.
- FIXED: 7470: Make it easier to create WebDriver instances in custom flows for
  parallel execution.

## v2.42.1

- FIXED: 7465: Fixed `net.getLoopbackAddress` on Windows
- FIXED: 7277: Support `done` callback in Mocha's BDD interface
- FIXED: 7156: `Promise#thenFinally` should not suppress original error

## v2.42.0

- Removed deprecated functions `Promise#addCallback()`,
  `Promise#addCallbacks()`, `Promise#addErrback()`, and `Promise#addBoth()`.
- Fail with a more descriptive error if the server returns a malformed redirect
- FIXED: 7300: Connect to ChromeDriver using the loopback address since
  ChromeDriver 2.10.267517
  binds to localhost by default.
- FIXED: 7339: Preserve wrapped test function's string representation for Mocha'
  s BDD interface.

## v2.41.0

- FIXED: 7138: export logging API from webdriver module.
- FIXED: 7105: beforeEach/it/afterEach properly bind `this` for Mocha tests.

## v2.40.0

- API documentation is now included in the docs directory.
- Added utility functions for working with an array of promises:
  `promise.all`, `promise.map`, and `promise.filter`
- Introduced `Promise#thenCatch()` and `Promise#thenFinally()`.
- Deprecated `Promise#addCallback()`, `Promise#addCallbacks()`,
  `Promise#addErrback()`, and `Promise#addBoth()`.
- Removed deprecated function `webdriver.WebDriver#getCapability`.
- FIXED: 6826: Added support for custom locators.

## v2.39.0

- Version bump to stay in sync with the Selenium project.

## v2.38.1

- FIXED: 6686: Changed `webdriver.promise.Deferred#cancel()` to silently no-op
  if the deferred has
  already been resolved.

## v2.38.0

- When a promise is rejected, always annotate the stacktrace with the parent
  flow state so users can
  identify the source of an error.
- Updated tests to reflect features not working correctly in the SafariDriver
  (cookie management and proxy support; see issues 5051, 5212, and 5503)
- FIXED: 6284: For mouse moves, correctly omit the x/y offsets if not specified
  as a function
  argument (instead of passing (0,0)).
- FIXED: 6471: Updated documentation on `webdriver.WebElement#getAttribute`
- FIXED: 6612: On Unix, use the default IANA ephemeral port range if unable to
  retrieve the current
  system's port range.
- FIXED: 6617: Avoid triggering the node debugger when initializing the
  stacktrace module.
- FIXED: 6627: Safely rebuild chrome.Options from a partial JSON spec.

## v2.37.0

- FIXED: 6346: The remote.SeleniumServer class now accepts JVM arguments using
  the `jvmArgs` option.

## v2.36.0

- _Release skipped to stay in sync with main Selenium project._

## v2.35.2

- FIXED: 6200: Pass arguments to the Selenium server instead of to the JVM.

## v2.35.1

- FIXED: 6090: Changed example scripts to use chromedriver.

## v2.35.0

- Version bump to stay in sync with the Selenium project.

## v2.34.1

- FIXED: 6079: The parent process should not wait for spawn driver service
  processes (chromedriver,
  phantomjs, etc.)

## v2.34.0

- Added the `selenium-webdriver/testing/assert` module. This module simplifies
  writing assertions
  against promised values (see example in module documentation).
- Added the `webdriver.Capabilities` class.
- Added native support for the ChromeDriver. When using the `Builder`,
  requesting chrome without
  specifying a remote server URL will default to the native ChromeDriver
  implementation. The
  [ChromeDriver server](https://code.google.com/p/chromedriver/downloads/list)
  must be downloaded separately.

        // Will start ChromeDriver locally.
        var driver = new webdriver.Builder().
            withCapabilities(webdriver.Capabilities.chrome()).
            build();

        // Will start ChromeDriver using the remote server.
        var driver = new webdriver.Builder().
            withCapabilities(webdriver.Capabilities.chrome()).
            usingServer('http://server:1234/wd/hub').
            build();

- Added support for configuring proxies through the builder. For examples, see
  `selenium-webdriver/test/proxy_test`.
- Added native support for PhantomJS.
- Changed signature of `SeleniumServer` to `SeleniumServer(jar, options)`.
- Tests are now included in the npm published package. See `README.md` for
  execution instructions
- Removed the deprecated `webdriver.Deferred#resolve` and
  `webdriver.promise.resolved` functions.
- Removed the ability to connect to an existing session from the Builder. This
  feature is intended
  for use with the browser-based client.

## v2.33.0

- Added support for WebDriver's logging API
- FIXED: 5511: Added webdriver.manage().timeouts().pageLoadTimeout(ms)

## v2.32.1

- FIXED: 5541: Added missing return statement for windows in
  `portprober.findFreePort()`

## v2.32.0

- Added the `selenium-webdriver/testing` package, which provides a basic
  framework for writing tests
  using Mocha. See
  `selenium-webdriver/example/google_search_test.js` for usage.
- For Promises/A+ compatibility, backing out the change in 2.30.0 that ensured
  rejections were
  always Error objects. Rejection reasons are now left as is.
- Removed deprecated functions originally scheduled for removal in 2.31.0
  - promise.Application.getInstance()
  - promise.ControlFlow#schedule()
  - promise.ControlFlow#scheduleTimeout()
  - promise.ControlFlow#scheduleWait()
- Renamed some functions for consistency with Promises/A+ terminology. The
  original functions have
  been deprecated and will be removed in 2.34.0:
  - promise.resolved() -> promise.fulfilled()
  - promise.Deferred#resolve() -> promise.Deferred#fulfill()
- FIXED: remote.SeleniumServer#stop now shuts down within the active control
  flow, allowing scripts
  to finish. Use #kill to shutdown immediately.
- FIXED: 5321: cookie deletion commands

## v2.31.0

- Added an example script.
- Added a class for controlling the standalone Selenium server (server available
  separately)
- Added a portprober for finding free ports
- FIXED: WebElements now belong to the same flow as their parent driver.

## v2.30.0

- Ensures promise rejections are always Error values.
- Version bump to keep in sync with the Selenium project.

## v2.29.1

- Fixed a bug that could lead to an infinite loop.
- Added a README.md

## v2.29.0

- Initial release for npm:

        npm install selenium-webdriver
