{"name": "selenium-webdriver", "version": "4.34.0", "description": "The official WebDriver JavaScript bindings from the Selenium project", "license": "Apache-2.0", "keywords": ["automation", "selenium", "testing", "webdriver", "webdriverjs"], "homepage": "https://github.com/SeleniumHQ/selenium/tree/trunk/javascript/selenium-webdriver#readme", "bugs": {"url": "https://github.com/SeleniumHQ/selenium/issues"}, "main": "./index", "repository": {"type": "git", "url": "https://github.com/SeleniumHQ/selenium.git"}, "engines": {"node": ">= 20.0.0"}, "dependencies": {"@bazel/runfiles": "^6.3.1", "jszip": "^3.10.1", "tmp": "^0.2.3", "ws": "^8.18.2"}, "devDependencies": {"@eslint/js": "^9.18.0", "clean-jsdoc-theme": "^4.3.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-mocha": "^10.5.0", "eslint-plugin-n": "^17.15.1", "eslint-plugin-no-only-tests": "^3.3.0", "eslint-plugin-prettier": "^5.2.1", "express": "^4.21.2", "globals": "^15.14.0", "has-flag": "^5.0.1", "jsdoc": "^4.0.4", "mocha": "^11.0.1", "mocha-junit-reporter": "^2.2.1", "multer": "1.4.5-lts.2", "prettier": "^3.4.2", "serve-index": "^1.9.1", "sinon": "^19.0.5", "supports-color": "^10.0.0"}, "scripts": {"lint": "eslint .", "lint:fix": "eslint . --fix", "test": "bazel test //javascript/selenium-webdriver/...", "generate-docs": "jsdoc --configure jsdoc_conf.json --verbose"}, "mocha": {"recursive": true, "timeout": 600000}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/SeleniumHQ"}, {"type": "opencollective", "url": "https://opencollective.com/selenium"}]}