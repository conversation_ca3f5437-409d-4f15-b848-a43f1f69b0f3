require('dotenv').config();
const { Builder, By, until, Key } = require('selenium-webdriver');
const firefox = require('selenium-webdriver/firefox');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const sharp = require('sharp');
const { Solver } = require('solvecaptcha-javascript');

// Create screenshots directory if it doesn't exist
const screenshotsDir = path.join(__dirname, 'screenshots');
console.log('Screenshots directory path:', screenshotsDir);

try {
    if (!fs.existsSync(screenshotsDir)) {
        fs.mkdirSync(screenshotsDir, { recursive: true });
        console.log('Created screenshots directory');
    } else {
        console.log('Screenshots directory already exists');
    }

    // Test write permissions
    const testFile = path.join(screenshotsDir, 'test.txt');
    fs.writeFileSync(testFile, 'test');
    fs.unlinkSync(testFile);
    console.log('Screenshots directory is writable');

} catch (dirError) {
    console.error('Error with screenshots directory:', dirError.message);
}

class CaptchaBypasser {
    constructor(apiKey) {
        this.driver = null;
        this.apiKey = apiKey;
        this.maxRetries = 3;
        this.waitTimeout = 30000;
        this.pollInterval = 2000;
        this.maxPollTime = 180000;
        this.maxRoundsPerChallenge = 2;
        this.challengeContainerSelector = null;

        this.submitUrl = 'https://api.solvecaptcha.com/in.php';
        this.resultUrl = 'https://api.solvecaptcha.com/res.php';

        this.solver = new Solver(apiKey);
    }

    async initializeDriver() {
        const options = new firefox.Options();

        // Firefox preferences for anti-detection
        options.setPreference('dom.webdriver.enabled', false);
        options.setPreference('useAutomationExtension', false);
        options.setPreference('general.useragent.override', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0');

        // Disable automation indicators
        options.setPreference('marionette.enabled', true);
        options.setPreference('browser.startup.page', 0);
        options.setPreference('browser.startup.homepage', 'about:blank');

        // Security and performance settings
        options.setPreference('security.tls.insecure_fallback_hosts', 'localhost');
        options.setPreference('browser.cache.disk.enable', false);
        options.setPreference('browser.cache.memory.enable', false);
        options.setPreference('browser.cache.offline.enable', false);

        // Set initial zoom level
        options.setPreference('layout.css.devPixelsPerPx', '0.8');

        this.driver = await new Builder()
            .forBrowser('firefox')
            .setFirefoxOptions(options)
            .build();

        // Set browser window size to be much larger
        await this.driver.manage().window().setRect({
            width: 1920,
            height: 1200,
            x: 0,
            y: 0
        });

        // Additional anti-detection measures
        await this.driver.executeScript(`
            Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
            Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
            Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']});
        `);

        // Set zoom to 80%
        await this.driver.executeScript("document.body.style.zoom = '0.8'");

        // Store zoom factor for coordinate calculations
        this.zoomFactor = 0.8;
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async dismissNotificationPopup() {
        try {
            console.log('Checking for notification popup...');

            // Wait a bit for popup to appear
            await this.sleep(2000);

            const popupSelectors = [
                '#onesignal-slidedown-cancel-button',
                'button[id*="cancel"]',
                'button[id*="no-thanks"]',
                'button:contains("No, thanks")',
                '.slidedown-button.secondary',
                '[class*="cancel"]',
                '[class*="dismiss"]',
                '[class*="close"]'
            ];

            for (const selector of popupSelectors) {
                try {
                    const elements = await this.driver.findElements(By.css(selector));
                    if (elements.length > 0) {
                        console.log(`Found notification popup, clicking dismiss with: ${selector}`);
                        await elements[0].click();
                        await this.sleep(1000);
                        return true;
                    }
                } catch (e) {
                    continue;
                }
            }

            // Try by text content
            const dismissedByText = await this.driver.executeScript(`
                const buttons = Array.from(document.querySelectorAll('button, div[role="button"], a'));
                const dismissTexts = ['no thanks', 'no, thanks', 'dismiss', 'close', 'cancel', 'not now'];

                for (const button of buttons) {
                    const text = button.textContent.toLowerCase().trim();
                    if (dismissTexts.some(dismissText => text.includes(dismissText))) {
                        console.log('Found dismiss button by text:', text);
                        try {
                            button.click();
                            return true;
                        } catch (e) {
                            console.log('Text-based dismiss click failed:', e.message);
                        }
                    }
                }
                return false;
            `);

            if (dismissedByText) {
                console.log('Notification popup dismissed by text content');
                return true;
            }

            console.log('No notification popup found');
            return false;

        } catch (error) {
            console.log('Error dismissing notification popup:', error.message);
            return false;
        }
    }

    async submitCaptcha(siteKey, pageUrl, captchaType = 'hcaptcha') {
        try {
            const payload = {
                key: this.apiKey,
                method: captchaType,
                pageurl: pageUrl,
                json: 1
            };

            if (captchaType === 'hcaptcha') {
                payload.sitekey = siteKey;
            } else {
                payload.googlekey = siteKey;
            }

            console.log('Submitting payload:', JSON.stringify(payload, null, 2));

            const response = await axios.post(this.submitUrl, payload, {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                transformRequest: [(data) => {
                    return Object.keys(data)
                        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(data[key])}`)
                        .join('&');
                }]
            });

            console.log('API Response:', JSON.stringify(response.data, null, 2));

            if (response.data.status === 1) {
                return response.data.request;
            } else {
                const errorText = response.data.error_text || response.data.error || 'Unknown error';
                throw new Error(`Captcha submission failed: ${errorText}`);
            }
        } catch (error) {
            if (error.response) {
                console.error('API Error Response:', error.response.data);
                console.error('API Error Status:', error.response.status);
            }
            console.error('Error submitting captcha:', error.message);
            throw error;
        }
    }

    async getCaptchaResult(requestId) {
        const startTime = Date.now();
        let pollCount = 0;

        console.log(`Starting to poll for captcha solution. Request ID: ${requestId}`);
        console.log(`Max poll time: ${this.maxPollTime}ms (${this.maxPollTime/1000} seconds)`);

        while (Date.now() - startTime < this.maxPollTime) {
            try {
                pollCount++;
                const elapsedTime = Date.now() - startTime;

                const response = await axios.get(this.resultUrl, {
                    params: {
                        key: this.apiKey,
                        action: 'get',
                        id: requestId,
                        json: 1
                    }
                });

                console.log(`Poll #${pollCount} (${Math.round(elapsedTime/1000)}s elapsed):`, JSON.stringify(response.data, null, 2));

                if (response.data.status === 1) {
                    console.log('Captcha solved successfully!');
                    return response.data.request;
                } else if (response.data.error_text === 'CAPCHA_NOT_READY' || response.data.request === 'CAPCHA_NOT_READY') {
                    if (pollCount % 10 === 0) {
                        console.log(`Still waiting... (${Math.round(elapsedTime/1000)}s elapsed, ${pollCount} polls)`);
                    }
                    await this.sleep(this.pollInterval);
                    continue;
                } else {
                    const errorText = response.data.error_text || response.data.error || 'Unknown error';
                    throw new Error(`Captcha solving failed: ${errorText}`);
                }
            } catch (error) {
                if (error.response) {
                    console.error('Polling Error Response:', error.response.data);
                }
                console.error('Error getting captcha result:', error.message);
                await this.sleep(this.pollInterval);
            }
        }

        console.error(`Captcha solving timeout after ${this.maxPollTime/1000} seconds and ${pollCount} polls`);
        throw new Error('Captcha solving timeout - service may be overloaded or captcha too complex');
    }

    async findCaptcha() {
        const selectors = [
            'iframe[src*="hcaptcha"]',
            '.h-captcha',
            '[data-sitekey]',
            'div[class*="hcaptcha"]',
            'iframe[src*="recaptcha"]',
            '.g-recaptcha',
            'div[class*="recaptcha"]'
        ];

        for (const selector of selectors) {
            try {
                const elements = await this.driver.findElements(By.css(selector));
                if (elements.length > 0) {
                    return elements[0];
                }
            } catch (error) {
                continue;
            }
        }
        return null;
    }

    async extractSiteKey() {
        try {
            console.log('Trying to find data-sitekey attribute...');
            const siteKeyElement = await this.driver.findElement(By.css('[data-sitekey]'));
            const siteKey = await siteKeyElement.getAttribute('data-sitekey');
            console.log('Found sitekey in data-sitekey attribute:', siteKey);
            return siteKey;
        } catch (error) {
            console.log('No data-sitekey attribute found, searching page source...');

            const pageSource = await this.driver.getPageSource();

            // Try multiple patterns
            const patterns = [
                /data-sitekey="([^"]+)"/,
                /sitekey=([a-f0-9-]{36})/g,
                /sitekey:\s*["']([^"']+)["']/,
                /"sitekey":\s*"([^"]+)"/,
                /hcaptcha.*sitekey[=:][\s"']*([a-f0-9-]{36})/i
            ];

            for (const pattern of patterns) {
                const match = pageSource.match(pattern);
                if (match) {
                    console.log('Found sitekey with pattern:', pattern.toString(), '-> ', match[1]);
                    return match[1];
                }
            }

            console.log('Searching in iframe sources...');
            const allIframes = await this.driver.findElements(By.css('iframe'));
            console.log(`Checking ${allIframes.length} iframes for sitekey...`);

            for (let i = 0; i < allIframes.length; i++) {
                try {
                    const src = await allIframes[i].getAttribute('src');
                    console.log(`Iframe ${i} src:`, src);

                    if (src && src.includes('hcaptcha')) {
                        const urlSiteKeyMatch = src.match(/sitekey=([a-f0-9-]{36})/);
                        if (urlSiteKeyMatch) {
                            console.log('Found sitekey in iframe URL:', urlSiteKeyMatch[1]);
                            return urlSiteKeyMatch[1];
                        }
                    }
                } catch (e) {
                    console.log(`Could not get src for iframe ${i}:`, e.message);
                }
            }

            // Last resort: look for any UUID-like string that might be a sitekey
            console.log('Last resort: searching for UUID patterns...');
            const uuidMatches = pageSource.match(/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}/g);
            if (uuidMatches && uuidMatches.length > 0) {
                console.log('Found potential sitekeys:', uuidMatches);
                // Return the first one that looks like an hCaptcha sitekey
                for (const uuid of uuidMatches) {
                    if (pageSource.includes(uuid) && (pageSource.includes('hcaptcha') || pageSource.includes('captcha'))) {
                        console.log('Using UUID as sitekey:', uuid);
                        return uuid;
                    }
                }
            }

            throw new Error('Could not find captcha site key after exhaustive search');
        }
    }

    async solveCaptcha() {
        try {
            console.log('Looking for captcha...');

            // Dismiss notification popup first
            await this.dismissNotificationPopup();

            let captchasSolved = 0;
            const maxCaptchas = 3; // Handle up to 3 captchas

            for (let attempt = 1; attempt <= maxCaptchas; attempt++) {
                console.log(`\n=== Captcha Attempt ${attempt}/${maxCaptchas} ===`);

                const captchaElement = await this.findCaptcha();
                if (!captchaElement) {
                    console.log('No captcha found');
                    if (captchasSolved > 0) {
                        console.log(`Successfully solved ${captchasSolved} captcha(s)`);
                        return true;
                    }
                    return false;
                }

                console.log('Captcha found, checking if interactive challenge is present...');

                // Check if there's an interactive hCaptcha challenge
                const interactiveChallenge = await this.driver.findElements(By.css('iframe[src*="hcaptcha.html#frame=challenge"]'));

                let solutionResult = false;
                if (interactiveChallenge.length > 0) {
                    console.log('Interactive hCaptcha challenge detected - attempting automated solution...');
                    solutionResult = await this.solveInteractiveHcaptcha();
                } else {
                    console.log('Simple captcha detected - using token-based solution...');
                    solutionResult = await this.solveTokenBasedCaptcha();
                }

                if (solutionResult) {
                    captchasSolved++;
                    console.log(`Captcha ${attempt} solved successfully!`);

                    // Wait longer for the next captcha to appear
                    console.log('Waiting for next captcha or login completion...');
                    await this.sleep(5000);

                    // Check multiple times for new captchas since they might take time to load
                    let foundNextCaptcha = false;
                    for (let checkAttempt = 1; checkAttempt <= 3; checkAttempt++) {
                        console.log(`Checking for additional captchas (attempt ${checkAttempt}/3)...`);

                        // Look for new captcha challenges
                        const moreCaptchas = await this.findCaptcha();
                        const interactiveChallenges = await this.driver.findElements(By.css('iframe[src*="hcaptcha.html#frame=challenge"]'));

                        if (moreCaptchas || interactiveChallenges.length > 0) {
                            console.log('Found another captcha challenge!');
                            foundNextCaptcha = true;
                            break;
                        }

                        // Check if we're actually logged in by looking for login-specific elements
                        const stillOnLoginPage = await this.driver.executeScript(`
                            // Check for login indicators
                            const loginIndicators = [
                                'input[name="username"]',
                                'input[name="password"]',
                                'button[data-testid="signin-nav"]',
                                '.login-form',
                                '.h-captcha',
                                'iframe[src*="hcaptcha"]'
                            ];

                            for (const selector of loginIndicators) {
                                if (document.querySelector(selector)) {
                                    console.log('Still on login page, found:', selector);
                                    return true;
                                }
                            }

                            // Check URL
                            if (window.location.href.includes('login') || window.location.href.includes('signin')) {
                                console.log('URL indicates still on login page');
                                return true;
                            }

                            return false;
                        `);

                        if (!stillOnLoginPage) {
                            console.log('Login appears to be complete - no login elements found');
                            return true;
                        }

                        console.log('Still on login page, waiting for next captcha...');
                        await this.sleep(2000);
                    }

                    if (!foundNextCaptcha) {
                        console.log('No additional captchas found after waiting');
                        // Don't return true here - continue the loop to check one more time
                    } else {
                        console.log('Proceeding to solve next captcha...');
                    }
                } else {
                    console.log(`Failed to solve captcha ${attempt}`);
                    // Continue to next attempt
                }
            }

            console.log(`Completed ${captchasSolved} captcha(s) out of ${maxCaptchas} attempts`);

            // Final check - make sure we're actually logged in
            await this.sleep(3000);
            const finalLoginCheck = await this.driver.executeScript(`
                // Look for indicators that we're logged in
                const loggedInIndicators = [
                    '[data-testid="user-menu"]',
                    '.user-profile',
                    '.account-menu',
                    '.logout-button',
                    '.user-balance'
                ];

                for (const selector of loggedInIndicators) {
                    if (document.querySelector(selector)) {
                        console.log('Found logged-in indicator:', selector);
                        return true;
                    }
                }

                // Check if we're no longer on login page
                const loginElements = document.querySelectorAll('input[name="username"], input[name="password"], .h-captcha');
                if (loginElements.length === 0 && !window.location.href.includes('login')) {
                    console.log('No login elements found and not on login URL');
                    return true;
                }

                return false;
            `);

            if (finalLoginCheck) {
                console.log('Final verification: Login successful!');
                return true;
            } else {
                console.log('Final verification: Still appears to be on login page');
                return captchasSolved > 0;
            }

        } catch (error) {
            console.error('Error solving captcha:', error.message);
            return false;
        }
    }

    async solveInteractiveHcaptcha() {
        try {
            console.log('Attempting to solve interactive hCaptcha...');

            // Wait for challenge to fully load
            await this.sleep(3000);

            // First, try to click the hCaptcha checkbox to trigger the challenge
            await this.clickHcaptchaCheckbox();

            // Wait for challenge to appear
            await this.sleep(3000);

            // Check if there's an interactive challenge
            const challengeIframe = await this.driver.findElements(By.css('iframe[src*="hcaptcha.html#frame=challenge"]'));
            if (challengeIframe.length > 0) {
                console.log('Interactive challenge detected, capturing and solving...');
                return await this.solveImageChallenge(challengeIframe[0]);
            }

            console.log('No interactive challenge appeared, captcha may be solved');
            return true;

        } catch (error) {
            console.error('Error solving interactive hCaptcha:', error.message);
            await this.driver.switchTo().defaultContent();
            return false;
        }
    }

    async clickHcaptchaCheckbox() {
        const checkboxSelectors = [
            'iframe[src*="hcaptcha"][src*="checkbox"]',
            'iframe[src*="hcaptcha.com"]',
            '.h-captcha iframe'
        ];

        for (const selector of checkboxSelectors) {
            try {
                const iframes = await this.driver.findElements(By.css(selector));
                if (iframes.length > 0) {
                    console.log(`Found hCaptcha checkbox iframe with: ${selector}`);
                    await this.driver.switchTo().frame(iframes[0]);

                    // Try to find and click checkbox
                    const checkboxElements = await this.driver.findElements(By.css('#checkbox, .check, [role="checkbox"], div[class*="checkbox"]'));
                    if (checkboxElements.length > 0) {
                        console.log('Clicking hCaptcha checkbox...');
                        await checkboxElements[0].click();
                    } else {
                        console.log('No specific checkbox found, clicking body...');
                        await this.driver.executeScript('document.body.click();');
                    }

                    await this.driver.switchTo().defaultContent();
                    return true;
                }
            } catch (error) {
                await this.driver.switchTo().defaultContent();
                continue;
            }
        }

        console.log('No hCaptcha checkbox found');
        return false;
    }

    async solveImageChallenge(challengeIframe) {
        try {
            // First, get the iframe position and size before switching to it
            console.log('Getting iframe position and size...');
            const iframeRect = await challengeIframe.getRect();
            console.log('Iframe dimensions:', iframeRect);

            // Switch to iframe to get instruction first
            console.log('Switching to challenge iframe...');
            await this.driver.switchTo().frame(challengeIframe);

            // Wait for challenge to load
            await this.sleep(2000);

            // Get the challenge instruction text
            const instructionElements = await this.driver.findElements(By.css('.prompt-text, .challenge-prompt, .task-text, h2, .instruction'));
            let instruction = 'Select all matching images';

            if (instructionElements.length > 0) {
                instruction = await instructionElements[0].getText();
                console.log('Challenge instruction:', instruction);
            }

            // Switch back to main content to take screenshot
            await this.driver.switchTo().defaultContent();

            // Take screenshot of main page and crop to iframe area
            console.log('Taking captcha area screenshot...');
            const fullScreenshot = await this.driver.takeScreenshot();

            // Crop the screenshot to just the iframe area
            const croppedScreenshot = await this.cropScreenshot(fullScreenshot, iframeRect);

            // Save cropped screenshot for debugging
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const screenshotPath = path.join(screenshotsDir, `captcha-cropped-${timestamp}.png`);

            try {
                fs.writeFileSync(screenshotPath, croppedScreenshot, 'base64');
                console.log(`Cropped captcha screenshot saved to: ${screenshotPath}`);
            } catch (saveError) {
                console.error('Error saving cropped screenshot:', saveError.message);
            }

            // Check if this is a puzzle/jigsaw challenge
            if (instruction.toLowerCase().includes('puzzle') ||
                instruction.toLowerCase().includes('ziehen') ||
                instruction.toLowerCase().includes('drag') ||
                instruction.toLowerCase().includes('vervollständigen')) {
                console.log('Detected puzzle challenge, using puzzle solver...');
                return await this.solvePuzzleChallenge(instruction, croppedScreenshot, iframeRect);
            }

            // Use the cropped screenshot for API
            const screenshot = croppedScreenshot;

            console.log('Sending challenge to SolveCaptcha API...');

            // Use the coordinates method for image selection challenges
            const result = await this.solver.coordinates({
                body: screenshot,
                textinstructions: instruction
            });

            console.log('Received solution:', result);

            // Handle different response formats
            let coordinates = [];
            if (result && result.data && Array.isArray(result.data)) {
                coordinates = result.data;
            } else if (result && result.coordinates && Array.isArray(result.coordinates)) {
                coordinates = result.coordinates;
            }

            if (coordinates.length > 0) {
                console.log(`Clicking on ${coordinates.length} solution coordinates...`);

                // Click on each coordinate (adjusted for zoom)
                for (let i = 0; i < coordinates.length; i++) {
                    const coord = coordinates[i];
                    const originalX = parseInt(coord.x);
                    const originalY = parseInt(coord.y);

                    // Don't adjust coordinates for Firefox - it handles zoom differently
                    const targetX = originalX;
                    const targetY = originalY;

                    console.log(`Clicking coordinate ${i + 1}/${coordinates.length}: (${targetX}, ${targetY})`);

                    try {
                        // Method 1: Find and click the actual element at coordinates
                        const clickResult = await this.driver.executeScript(`
                            const x = ${targetX};
                            const y = ${targetY};

                            // Find element at coordinates
                            const element = document.elementFromPoint(x, y);
                            console.log('Element at (' + x + ', ' + y + '):', element);

                            if (element) {
                                // Try multiple click methods
                                let clicked = false;

                                // Method 1: Direct click
                                try {
                                    element.click();
                                    clicked = true;
                                    console.log('Direct click successful on:', element.tagName, element.className);
                                } catch (e) {
                                    console.log('Direct click failed:', e.message);
                                }

                                // Method 2: Mouse events
                                if (!clicked) {
                                    try {
                                        const mouseDown = new MouseEvent('mousedown', {
                                            bubbles: true,
                                            cancelable: true,
                                            clientX: x,
                                            clientY: y
                                        });
                                        const mouseUp = new MouseEvent('mouseup', {
                                            bubbles: true,
                                            cancelable: true,
                                            clientX: x,
                                            clientY: y
                                        });
                                        const click = new MouseEvent('click', {
                                            bubbles: true,
                                            cancelable: true,
                                            clientX: x,
                                            clientY: y
                                        });

                                        element.dispatchEvent(mouseDown);
                                        element.dispatchEvent(mouseUp);
                                        element.dispatchEvent(click);
                                        clicked = true;
                                        console.log('Mouse events successful');
                                    } catch (e) {
                                        console.log('Mouse events failed:', e.message);
                                    }
                                }

                                // Method 3: Try parent element if it's an image
                                if (!clicked && element.tagName === 'IMG' && element.parentElement) {
                                    try {
                                        element.parentElement.click();
                                        clicked = true;
                                        console.log('Parent click successful');
                                    } catch (e) {
                                        console.log('Parent click failed:', e.message);
                                    }
                                }

                                // Method 4: Try finding clickable parent
                                if (!clicked) {
                                    let parent = element.parentElement;
                                    while (parent && parent !== document.body) {
                                        if (parent.onclick || parent.addEventListener ||
                                            parent.classList.contains('clickable') ||
                                            parent.role === 'button' ||
                                            parent.tagName === 'BUTTON' ||
                                            parent.tagName === 'A') {
                                            try {
                                                parent.click();
                                                clicked = true;
                                                console.log('Clickable parent found and clicked:', parent.tagName, parent.className);
                                                break;
                                            } catch (e) {
                                                console.log('Clickable parent click failed:', e.message);
                                            }
                                        }
                                        parent = parent.parentElement;
                                    }
                                }

                                return {
                                    success: clicked,
                                    element: element.tagName + ' ' + element.className,
                                    coordinates: {x: x, y: y}
                                };
                            } else {
                                console.log('No element found at coordinates');
                                return {success: false, coordinates: {x: x, y: y}};
                            }
                        `);

                        console.log('Click result:', clickResult);

                        // Method 2: Selenium Actions as backup
                        if (!clickResult.success) {
                            console.log('Trying Selenium Actions as backup...');
                            const actions = this.driver.actions();
                            await actions.move({x: targetX, y: targetY}).click().perform();
                        }

                        await this.sleep(500);

                    } catch (clickError) {
                        console.error(`Error clicking coordinate (${targetX}, ${targetY}):`, clickError.message);
                    }
                }

                // Add visual feedback to see what was clicked
                await this.driver.executeScript(`
                    // Highlight clicked elements
                    const clickedElements = document.querySelectorAll('.hcaptcha-clicked');
                    clickedElements.forEach(el => {
                        el.style.border = '3px solid red';
                        el.style.boxShadow = '0 0 10px red';
                    });
                `);

                await this.sleep(2000);

                // Look for and click submit button with more comprehensive search
                console.log('Looking for submit button...');

                const submitSelectors = [
                    '.button-submit',
                    '.verify-button',
                    'button[type="submit"]',
                    '.submit-btn',
                    '[class*="submit"]',
                    '[class*="verify"]',
                    '[class*="check"]',
                    'button[role="button"]',
                    'div[role="button"]',
                    '.hcaptcha-submit',
                    '.challenge-button'
                ];

                let submitClicked = false;

                // First, try to find submit button by text content
                const submitByText = await this.driver.executeScript(`
                    const buttons = Array.from(document.querySelectorAll('button, div[role="button"], [class*="button"]'));
                    const submitTexts = ['submit', 'verify', 'check', 'prüfen', 'bestätigen', 'weiter'];

                    for (const button of buttons) {
                        const text = button.textContent.toLowerCase().trim();
                        if (submitTexts.some(submitText => text.includes(submitText))) {
                            console.log('Found submit button by text:', text);
                            try {
                                button.click();
                                return true;
                            } catch (e) {
                                console.log('Text-based submit click failed:', e.message);
                            }
                        }
                    }
                    return false;
                `);

                if (submitByText) {
                    console.log('Submit button clicked by text content');
                    submitClicked = true;
                } else {
                    // Try selectors
                    for (const selector of submitSelectors) {
                        try {
                            const submitButtons = await this.driver.findElements(By.css(selector));
                            if (submitButtons.length > 0) {
                                console.log(`Found submit button with selector: ${selector}`);
                                await submitButtons[0].click();
                                submitClicked = true;
                                break;
                            }
                        } catch (e) {
                            continue;
                        }
                    }
                }

                if (!submitClicked) {
                    console.log('No submit button found, trying Enter key and space...');
                    await this.driver.executeScript(`
                        document.body.dispatchEvent(new KeyboardEvent("keydown", {key: "Enter", bubbles: true}));
                        document.body.dispatchEvent(new KeyboardEvent("keydown", {key: " ", bubbles: true}));
                    `);
                }

                await this.driver.switchTo().defaultContent();
                await this.sleep(3000);

                return true;
            }

            await this.driver.switchTo().defaultContent();
            return false;

        } catch (error) {
            console.error('Error solving image challenge:', error.message);
            await this.driver.switchTo().defaultContent();
            return false;
        }
    }

    async solvePuzzleChallenge(instruction, screenshot, iframeRect) {
        try {
            console.log('Attempting to solve puzzle challenge...');

            // Use the provided screenshot (already cropped to captcha area)
            // Save puzzle screenshot for debugging
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const screenshotPath = path.join(screenshotsDir, `puzzle-cropped-${timestamp}.png`);

            try {
                fs.writeFileSync(screenshotPath, screenshot, 'base64');
                console.log(`Puzzle cropped screenshot saved to: ${screenshotPath}`);
            } catch (saveError) {
                console.error('Error saving puzzle screenshot:', saveError.message);
                console.log('Screenshots directory:', screenshotsDir);
                console.log('Directory exists:', fs.existsSync(screenshotsDir));
            }

            console.log('Sending puzzle to SolveCaptcha API using coordinates method...');

            // Use coordinates method for puzzle challenges since jigsaw might not be available
            const result = await this.solver.coordinates({
                body: screenshot,
                textinstructions: instruction
            });

            console.log('Received puzzle solution:', result);

            // Handle different response formats for puzzle
            let coordinates = [];
            if (result && result.data && Array.isArray(result.data)) {
                coordinates = result.data;
            } else if (result && result.coordinates && Array.isArray(result.coordinates)) {
                coordinates = result.coordinates;
            } else if (result && result.solution) {
                // Handle solution format
                if (result.solution.coordinates) {
                    coordinates = result.solution.coordinates;
                } else if (result.solution.x && result.solution.y) {
                    coordinates = [{x: result.solution.x, y: result.solution.y}];
                }
            }

            if (coordinates.length > 0) {
                console.log('Executing puzzle solution with drag operations...');

                // For puzzle challenges, treat coordinates as drag operations
                // Switch back to iframe for drag operations
                const challengeIframe = await this.driver.findElement(By.css('iframe[src*="hcaptcha.html#frame=challenge"]'));
                await this.driver.switchTo().frame(challengeIframe);

                if (coordinates.length >= 2) {
                    // Multiple coordinates - drag from first to last
                    const startCoord = coordinates[0];
                    const endCoord = coordinates[coordinates.length - 1];

                    const startX = parseInt(startCoord.x);
                    const startY = parseInt(startCoord.y);
                    const endX = parseInt(endCoord.x);
                    const endY = parseInt(endCoord.y);

                    console.log(`Dragging from (${startX}, ${startY}) to (${endX}, ${endY})`);
                    await this.performDragOperation({x: startX, y: startY}, {x: endX, y: endY});

                } else if (coordinates.length === 1) {
                    // Single coordinate - drag from center to this point
                    const coord = coordinates[0];
                    const targetX = parseInt(coord.x);
                    const targetY = parseInt(coord.y);

                    // Get puzzle center relative to iframe
                    const centerX = iframeRect.width / 2;
                    const centerY = iframeRect.height / 2;

                    console.log(`Dragging from center (${centerX}, ${centerY}) to (${targetX}, ${targetY})`);
                    await this.performDragOperation({x: centerX, y: centerY}, {x: targetX, y: targetY});
                }

                await this.sleep(1000);

                // Click submit button
                const submitButtons = await this.driver.findElements(By.css('.button-submit, .verify-button, button[type="submit"]'));
                if (submitButtons.length > 0) {
                    console.log('Clicking submit button...');
                    await submitButtons[0].click();
                }

                return true;
            } else {
                console.log('No coordinates received for puzzle solution');
                return false;
            }

        } catch (error) {
            console.error('Error solving puzzle challenge:', error.message);
            return false;
        }
    }

    async cropScreenshot(base64Screenshot, iframeRect) {
        try {
            console.log(`Cropping image to captcha area: x=${Math.round(iframeRect.x)}, y=${Math.round(iframeRect.y)}, width=${Math.round(iframeRect.width)}, height=${Math.round(iframeRect.height)}`);

            // Convert base64 to buffer
            const imageBuffer = Buffer.from(base64Screenshot, 'base64');

            // Crop the image to just the iframe area
            const croppedBuffer = await sharp(imageBuffer)
                .extract({
                    left: Math.round(iframeRect.x),
                    top: Math.round(iframeRect.y),
                    width: Math.round(iframeRect.width),
                    height: Math.round(iframeRect.height)
                })
                .png()
                .toBuffer();

            // Convert back to base64
            const croppedBase64 = croppedBuffer.toString('base64');

            console.log(`Original image size: ${imageBuffer.length} bytes`);
            console.log(`Cropped image size: ${croppedBuffer.length} bytes`);

            return croppedBase64;
        } catch (error) {
            console.error('Error cropping screenshot:', error.message);
            console.log('Falling back to original screenshot');
            return base64Screenshot;
        }
    }

    async compressImage(base64Image) {
        // Simple compression by reducing quality/size
        // In a real implementation, you might use a proper image compression library
        try {
            // For now, just return the original image
            // You could implement actual compression here if needed
            return base64Image;
        } catch (error) {
            console.error('Error compressing image:', error.message);
            return base64Image;
        }
    }

    async dragElement(from, to) {
        try {
            // Adjust coordinates for zoom
            const adjustedFromX = Math.round(from.x * (this.zoomFactor || 0.8));
            const adjustedFromY = Math.round(from.y * (this.zoomFactor || 0.8));
            const adjustedToX = Math.round(to.x * (this.zoomFactor || 0.8));
            const adjustedToY = Math.round(to.y * (this.zoomFactor || 0.8));

            console.log(`Dragging from (${from.x}, ${from.y}) -> (${adjustedFromX}, ${adjustedFromY}) to (${to.x}, ${to.y}) -> (${adjustedToX}, ${adjustedToY})`);

            const actions = this.driver.actions();
            await actions
                .move({x: adjustedFromX, y: adjustedFromY})
                .press()
                .move({x: adjustedToX, y: adjustedToY})
                .release()
                .perform();
        } catch (error) {
            console.error('Error dragging element:', error.message);
        }
    }

    async performDragOperation(from, to) {
        try {
            console.log(`Performing drag operation from (${from.x}, ${from.y}) to (${to.x}, ${to.y})`);

            // Method 1: Selenium Actions API
            try {
                const actions = this.driver.actions();
                await actions
                    .move({x: from.x, y: from.y})
                    .press()
                    .pause(100)
                    .move({x: to.x, y: to.y})
                    .pause(100)
                    .release()
                    .perform();
                console.log('Selenium drag completed');
            } catch (seleniumError) {
                console.log('Selenium drag failed:', seleniumError.message);
            }

            // Method 2: JavaScript drag simulation
            await this.driver.executeScript(`
                function simulateDrag(fromX, fromY, toX, toY) {
                    const startElement = document.elementFromPoint(fromX, fromY);
                    const endElement = document.elementFromPoint(toX, toY);

                    console.log('Start element:', startElement);
                    console.log('End element:', endElement);

                    if (startElement) {
                        // Create and dispatch drag events
                        const mouseDown = new MouseEvent('mousedown', {
                            bubbles: true,
                            cancelable: true,
                            clientX: fromX,
                            clientY: fromY,
                            button: 0
                        });

                        const dragStart = new DragEvent('dragstart', {
                            bubbles: true,
                            cancelable: true,
                            clientX: fromX,
                            clientY: fromY
                        });

                        const dragOver = new DragEvent('dragover', {
                            bubbles: true,
                            cancelable: true,
                            clientX: toX,
                            clientY: toY
                        });

                        const drop = new DragEvent('drop', {
                            bubbles: true,
                            cancelable: true,
                            clientX: toX,
                            clientY: toY
                        });

                        const mouseUp = new MouseEvent('mouseup', {
                            bubbles: true,
                            cancelable: true,
                            clientX: toX,
                            clientY: toY,
                            button: 0
                        });

                        // Dispatch events in sequence
                        startElement.dispatchEvent(mouseDown);
                        startElement.dispatchEvent(dragStart);

                        if (endElement) {
                            endElement.dispatchEvent(dragOver);
                            endElement.dispatchEvent(drop);
                        }

                        startElement.dispatchEvent(mouseUp);

                        console.log('JavaScript drag events dispatched');
                        return true;
                    }
                    return false;
                }

                return simulateDrag(${from.x}, ${from.y}, ${to.x}, ${to.y});
            `);

            // Method 3: Touch events for mobile-like behavior
            await this.driver.executeScript(`
                function simulateTouch(fromX, fromY, toX, toY) {
                    const startElement = document.elementFromPoint(fromX, fromY);

                    if (startElement) {
                        const touchStart = new TouchEvent('touchstart', {
                            bubbles: true,
                            cancelable: true,
                            touches: [{
                                clientX: fromX,
                                clientY: fromY,
                                target: startElement
                            }]
                        });

                        const touchMove = new TouchEvent('touchmove', {
                            bubbles: true,
                            cancelable: true,
                            touches: [{
                                clientX: toX,
                                clientY: toY,
                                target: startElement
                            }]
                        });

                        const touchEnd = new TouchEvent('touchend', {
                            bubbles: true,
                            cancelable: true,
                            changedTouches: [{
                                clientX: toX,
                                clientY: toY,
                                target: startElement
                            }]
                        });

                        startElement.dispatchEvent(touchStart);
                        setTimeout(() => startElement.dispatchEvent(touchMove), 50);
                        setTimeout(() => startElement.dispatchEvent(touchEnd), 100);

                        console.log('Touch drag events dispatched');
                        return true;
                    }
                    return false;
                }

                return simulateTouch(${from.x}, ${from.y}, ${to.x}, ${to.y});
            `);

            await this.sleep(500);
            console.log('Drag operation completed');

        } catch (error) {
            console.error('Error in drag operation:', error.message);
        }
    }

    async solveTokenBasedCaptcha() {
        try {
            console.log('Extracting site key...');
            const siteKey = await this.extractSiteKey();
            const currentUrl = await this.driver.getCurrentUrl();

            console.log(`Site key: ${siteKey}`);

            const isHcaptcha = await this.driver.findElements(By.css('iframe[src*="hcaptcha"], .h-captcha, div[class*="hcaptcha"]'));
            const captchaType = isHcaptcha.length > 0 ? 'hcaptcha' : 'recaptchav2';

            console.log(`Captcha type: ${captchaType}`);
            console.log('Submitting captcha to SolveCaptcha...');

            const requestId = await this.submitCaptcha(siteKey, currentUrl, captchaType);
            console.log(`Request ID: ${requestId}`);

            console.log('Waiting for captcha solution...');
            const solution = await this.getCaptchaResult(requestId);
            console.log('Captcha solution received:', solution.substring(0, 50) + '...');

            console.log('Injecting captcha solution...');
            if (captchaType === 'hcaptcha') {
                await this.driver.executeScript(`
                    const responseElements = [
                        document.querySelector('[name="h-captcha-response"]'),
                        document.querySelector('textarea[name="h-captcha-response"]'),
                        document.querySelector('#h-captcha-response'),
                        document.querySelector('.h-captcha-response')
                    ].filter(el => el);

                    responseElements.forEach(el => {
                        el.innerHTML = '${solution}';
                        el.value = '${solution}';
                        el.style.display = 'block';
                    });

                    if (typeof hcaptcha !== 'undefined') {
                        hcaptcha.getResponse = function() { return '${solution}'; };
                        // Trigger callback if exists
                        if (window.hcaptchaCallback) {
                            window.hcaptchaCallback('${solution}');
                        }
                    }

                    // Dispatch events to notify the page
                    const event = new Event('change', { bubbles: true });
                    responseElements.forEach(el => el.dispatchEvent(event));

                    console.log('hCaptcha solution injected into', responseElements.length, 'elements');
                `);
            } else {
                await this.driver.executeScript(`
                    const responseElement = document.getElementById('g-recaptcha-response');
                    if (responseElement) {
                        responseElement.innerHTML = '${solution}';
                        responseElement.value = '${solution}';
                        responseElement.style.display = 'block';

                        // Trigger callback
                        if (typeof grecaptcha !== 'undefined' && window.grecaptchaCallback) {
                            window.grecaptchaCallback('${solution}');
                        }
                    }
                `);
            }

            await this.sleep(2000);
            return true;

        } catch (error) {
            console.error('Error in token-based captcha solution:', error.message);
            return false;
        }
    }

    async loginToGamdom(username, password, config) {
        try {
            console.log('Navigating to Gamdom...');
            await this.driver.get(config.settings.target_url);
            await this.sleep(3000);

            // Take initial screenshot to test saving
            console.log('Taking initial screenshot...');
            const initialScreenshot = await this.driver.takeScreenshot();
            const initialTimestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const initialPath = path.join(screenshotsDir, `initial-${initialTimestamp}.png`);

            try {
                fs.writeFileSync(initialPath, initialScreenshot, 'base64');
                console.log(`Initial screenshot saved to: ${initialPath}`);
            } catch (saveError) {
                console.error('Error saving initial screenshot:', saveError.message);
                console.log('Screenshots directory:', screenshotsDir);
                console.log('Directory exists:', fs.existsSync(screenshotsDir));
                console.log('Directory permissions:', fs.statSync(screenshotsDir));
            }

            // Dismiss any notification popups
            await this.dismissNotificationPopup();

            console.log('Looking for login button...');
            const loginSelectors = config.selectors.login_button.join(', ');
            const loginButton = await this.driver.wait(
                until.elementLocated(By.css(loginSelectors)),
                this.waitTimeout
            );
            await loginButton.click();
            await this.sleep(2000);

            console.log('Entering credentials...');
            const usernameSelectors = config.selectors.username_field.join(', ');
            const usernameField = await this.driver.wait(
                until.elementLocated(By.css(usernameSelectors)),
                this.waitTimeout
            );
            await usernameField.clear();
            await usernameField.sendKeys(username);

            const passwordSelectors = config.selectors.password_field.join(', ');
            const passwordField = await this.driver.findElement(By.css(passwordSelectors));
            await passwordField.clear();
            await passwordField.sendKeys(password);

            console.log('Pressing Enter to submit credentials...');
            await passwordField.sendKeys(Key.RETURN);

            await this.sleep(2000);

            console.log('Checking for captcha after form submission...');
            await this.solveCaptcha();

            await this.sleep(5000);

            const currentUrl = await this.driver.getCurrentUrl();
            if (currentUrl.includes('dashboard') || currentUrl.includes('account') || !currentUrl.includes('login')) {
                console.log('Login successful!');
                return true;
            } else {
                console.log('Login may have failed - still on login page');
                return false;
            }

        } catch (error) {
            console.error('Login error:', error.message);
            return false;
        }
    }

    async close() {
        if (this.driver) {
            await this.driver.quit();
        }
    }
}

async function main() {
    let config;
    try {
        config = JSON.parse(fs.readFileSync('config.json', 'utf8'));
    } catch (error) {
        console.error('Error reading config.json:', error.message);
        config = {
            credentials: {
                username: process.env.GAMDOM_USERNAME || "oppenheimergang",
                password: process.env.GAMDOM_PASSWORD || "Z45@PKMG8JY2nfb",
                solvecaptcha_api_key: process.env.SOLVECAPTCHA_APIKEY || "59ff8b9c8f70d1cc677fb15c1a51ecb9"
            },
            settings: {
                target_url: "https://gamdom.io/"
            },
            selectors: {
                login_button: ["button[data-testid=\"signin-nav\"]"],
                username_field: ["input[name=\"username\"]"],
                password_field: ["input[name=\"password\"]"],
                submit_button: ["button[data-testid=\"start-playing-login\"]"]
            }
        };
    }

    const apiKey = process.env.SOLVECAPTCHA_APIKEY || config.credentials.solvecaptcha_api_key;
    const bypasser = new CaptchaBypasser(apiKey);
    
    try {
        await bypasser.initializeDriver();
        const success = await bypasser.loginToGamdom(config.credentials.username, config.credentials.password, config);
        
        if (success) {
            console.log('Successfully logged in and bypassed captcha!');
            await bypasser.sleep(10000);
        } else {
            console.log('Login failed');
        }
        
    } catch (error) {
        console.error('Script error:', error.message);
    } finally {
        await bypasser.close();
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = CaptchaBypasser;
