const { Builder, By, until, Key } = require('selenium-webdriver');
const chrome = require('selenium-webdriver/chrome');
const axios = require('axios');
const fs = require('fs');

class CaptchaBypasser {
    constructor(apiKey) {
        this.driver = null;
        this.apiKey = apiKey;
        this.maxRetries = 3;
        this.waitTimeout = 30000;
        this.pollInterval = 2000;
        this.maxPollTime = 180000;
        this.maxRoundsPerChallenge = 2;
        this.challengeContainerSelector = null;

        this.submitUrl = 'https://api.solvecaptcha.com/in.php';
        this.resultUrl = 'https://api.solvecaptcha.com/res.php';
    }

    async initializeDriver() {
        const options = new chrome.Options();
        options.addArguments('--disable-blink-features=AutomationControlled');
        options.addArguments('--disable-web-security');
        options.addArguments('--allow-running-insecure-content');
        options.addArguments('--no-sandbox');
        options.addArguments('--disable-dev-shm-usage');
        options.addArguments('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

        this.driver = await new Builder()
            .forBrowser('chrome')
            .setChromeOptions(options)
            .build();

        await this.driver.executeScript("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})");
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async submitCaptcha(siteKey, pageUrl, captchaType = 'recaptchav2') {
        try {
            const response = await axios.post(this.submitUrl, {
                key: this.apiKey,
                method: captchaType,
                googlekey: siteKey,
                pageurl: pageUrl,
                json: 1
            });

            if (response.data.status === 1) {
                return response.data.request;
            } else {
                throw new Error(`Captcha submission failed: ${response.data.error_text}`);
            }
        } catch (error) {
            console.error('Error submitting captcha:', error.message);
            throw error;
        }
    }

    async getCaptchaResult(requestId) {
        const startTime = Date.now();
        
        while (Date.now() - startTime < this.maxPollTime) {
            try {
                const response = await axios.get(this.resultUrl, {
                    params: {
                        key: this.apiKey,
                        action: 'get',
                        id: requestId,
                        json: 1
                    }
                });

                if (response.data.status === 1) {
                    return response.data.request;
                } else if (response.data.error_text === 'CAPCHA_NOT_READY') {
                    await this.sleep(this.pollInterval);
                    continue;
                } else {
                    throw new Error(`Captcha solving failed: ${response.data.error_text}`);
                }
            } catch (error) {
                console.error('Error getting captcha result:', error.message);
                await this.sleep(this.pollInterval);
            }
        }
        
        throw new Error('Captcha solving timeout');
    }

    async findRecaptcha() {
        const selectors = [
            'iframe[src*="recaptcha"]',
            '.g-recaptcha',
            '[data-sitekey]',
            'div[class*="recaptcha"]'
        ];

        for (const selector of selectors) {
            try {
                const elements = await this.driver.findElements(By.css(selector));
                if (elements.length > 0) {
                    return elements[0];
                }
            } catch (error) {
                continue;
            }
        }
        return null;
    }

    async extractSiteKey() {
        try {
            const siteKeyElement = await this.driver.findElement(By.css('[data-sitekey]'));
            return await siteKeyElement.getAttribute('data-sitekey');
        } catch (error) {
            const pageSource = await this.driver.getPageSource();
            const siteKeyMatch = pageSource.match(/data-sitekey="([^"]+)"/);
            if (siteKeyMatch) {
                return siteKeyMatch[1];
            }
            throw new Error('Could not find reCAPTCHA site key');
        }
    }

    async solveCaptcha() {
        console.log('Looking for captcha...');
        
        const captchaElement = await this.findRecaptcha();
        if (!captchaElement) {
            console.log('No captcha found');
            return false;
        }

        console.log('Captcha found, extracting site key...');
        const siteKey = await this.extractSiteKey();
        const currentUrl = await this.driver.getCurrentUrl();
        
        console.log(`Site key: ${siteKey}`);
        console.log('Submitting captcha to SolveCaptcha...');
        
        const requestId = await this.submitCaptcha(siteKey, currentUrl);
        console.log(`Request ID: ${requestId}`);
        
        console.log('Waiting for captcha solution...');
        const solution = await this.getCaptchaResult(requestId);
        console.log('Captcha solved!');
        
        await this.driver.executeScript(`
            document.getElementById('g-recaptcha-response').innerHTML = '${solution}';
            if (typeof grecaptcha !== 'undefined') {
                grecaptcha.getResponse = function() { return '${solution}'; };
            }
        `);

        const submitButton = await this.driver.findElement(By.css('input[type="submit"], button[type="submit"], .submit-btn, #submit'));
        await submitButton.click();
        
        return true;
    }

    async loginToGamdom(username, password) {
        try {
            console.log('Navigating to Gamdom...');
            await this.driver.get('https://gamdom.io/');
            await this.sleep(3000);

            console.log('Looking for login button...');
            const loginButton = await this.driver.wait(
                until.elementLocated(By.css('button[data-testid="login-button"], .login-btn, a[href*="login"]')),
                this.waitTimeout
            );
            await loginButton.click();
            await this.sleep(2000);

            console.log('Entering credentials...');
            const usernameField = await this.driver.wait(
                until.elementLocated(By.css('input[name="username"], input[type="email"], #username')),
                this.waitTimeout
            );
            await usernameField.clear();
            await usernameField.sendKeys(username);

            const passwordField = await this.driver.findElement(By.css('input[name="password"], input[type="password"], #password'));
            await passwordField.clear();
            await passwordField.sendKeys(password);

            await this.sleep(1000);

            const captchaSolved = await this.solveCaptcha();
            
            if (!captchaSolved) {
                const submitButton = await this.driver.findElement(By.css('button[type="submit"], input[type="submit"], .login-submit'));
                await submitButton.click();
            }

            await this.sleep(5000);
            
            const currentUrl = await this.driver.getCurrentUrl();
            if (currentUrl.includes('dashboard') || currentUrl.includes('account') || !currentUrl.includes('login')) {
                console.log('Login successful!');
                return true;
            } else {
                console.log('Login may have failed - still on login page');
                return false;
            }

        } catch (error) {
            console.error('Login error:', error.message);
            return false;
        }
    }

    async close() {
        if (this.driver) {
            await this.driver.quit();
        }
    }
}

async function main() {
    let config;
    try {
        config = JSON.parse(fs.readFileSync('config.json', 'utf8'));
    } catch (error) {
        console.error('Error reading config.json:', error.message);
        config = {
            credentials: {
                username: "oppenheimergang",
                password: "Z45@PKMG8JY2nfb",
                solvecaptcha_api_key: "59ff8b9c8f70d1cc677fb15c1a51ecb9"
            }
        };
    }

    const bypasser = new CaptchaBypasser(config.credentials.solvecaptcha_api_key);
    
    try {
        await bypasser.initializeDriver();
        const success = await bypasser.loginToGamdom(config.credentials.username, config.credentials.password);
        
        if (success) {
            console.log('Successfully logged in and bypassed captcha!');
            await bypasser.sleep(10000);
        } else {
            console.log('Login failed');
        }
        
    } catch (error) {
        console.error('Script error:', error.message);
    } finally {
        await bypasser.close();
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = CaptchaBypasser;
