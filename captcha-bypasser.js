const { Builder, By, until } = require('selenium-webdriver');
const chrome = require('selenium-webdriver/chrome');
const axios = require('axios');
const fs = require('fs');

class CaptchaBypasser {
    constructor(apiKey) {
        this.driver = null;
        this.apiKey = apiKey;
        this.maxRetries = 3;
        this.waitTimeout = 30000;
        this.pollInterval = 2000;
        this.maxPollTime = 180000;
        this.maxRoundsPerChallenge = 2;
        this.challengeContainerSelector = null;

        this.submitUrl = 'https://api.solvecaptcha.com/in.php';
        this.resultUrl = 'https://api.solvecaptcha.com/res.php';
    }

    async initializeDriver() {
        const options = new chrome.Options();
        options.addArguments('--disable-blink-features=AutomationControlled');
        options.addArguments('--disable-web-security');
        options.addArguments('--allow-running-insecure-content');
        options.addArguments('--no-sandbox');
        options.addArguments('--disable-dev-shm-usage');
        options.addArguments('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

        this.driver = await new Builder()
            .forBrowser('chrome')
            .setChromeOptions(options)
            .build();

        await this.driver.executeScript("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})");
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async submitCaptcha(siteKey, pageUrl, captchaType = 'hcaptcha') {
        try {
            const payload = {
                key: this.apiKey,
                method: captchaType,
                pageurl: pageUrl,
                json: 1
            };

            if (captchaType === 'hcaptcha') {
                payload.sitekey = siteKey;
            } else {
                payload.googlekey = siteKey;
            }

            console.log('Submitting payload:', JSON.stringify(payload, null, 2));

            const response = await axios.post(this.submitUrl, payload, {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                transformRequest: [(data) => {
                    return Object.keys(data)
                        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(data[key])}`)
                        .join('&');
                }]
            });

            console.log('API Response:', JSON.stringify(response.data, null, 2));

            if (response.data.status === 1) {
                return response.data.request;
            } else {
                const errorText = response.data.error_text || response.data.error || 'Unknown error';
                throw new Error(`Captcha submission failed: ${errorText}`);
            }
        } catch (error) {
            if (error.response) {
                console.error('API Error Response:', error.response.data);
                console.error('API Error Status:', error.response.status);
            }
            console.error('Error submitting captcha:', error.message);
            throw error;
        }
    }

    async getCaptchaResult(requestId) {
        const startTime = Date.now();

        while (Date.now() - startTime < this.maxPollTime) {
            try {
                const response = await axios.get(this.resultUrl, {
                    params: {
                        key: this.apiKey,
                        action: 'get',
                        id: requestId,
                        json: 1
                    }
                });

                console.log('Polling response:', JSON.stringify(response.data, null, 2));

                if (response.data.status === 1) {
                    return response.data.request;
                } else if (response.data.error_text === 'CAPCHA_NOT_READY' || response.data.request === 'CAPCHA_NOT_READY') {
                    console.log('Captcha not ready, waiting...');
                    await this.sleep(this.pollInterval);
                    continue;
                } else {
                    const errorText = response.data.error_text || response.data.error || 'Unknown error';
                    throw new Error(`Captcha solving failed: ${errorText}`);
                }
            } catch (error) {
                if (error.response) {
                    console.error('Polling Error Response:', error.response.data);
                }
                console.error('Error getting captcha result:', error.message);
                await this.sleep(this.pollInterval);
            }
        }

        throw new Error('Captcha solving timeout');
    }

    async findCaptcha() {
        const selectors = [
            'iframe[src*="hcaptcha"]',
            '.h-captcha',
            '[data-sitekey]',
            'div[class*="hcaptcha"]',
            'iframe[src*="recaptcha"]',
            '.g-recaptcha',
            'div[class*="recaptcha"]'
        ];

        for (const selector of selectors) {
            try {
                const elements = await this.driver.findElements(By.css(selector));
                if (elements.length > 0) {
                    return elements[0];
                }
            } catch (error) {
                continue;
            }
        }
        return null;
    }

    async extractSiteKey() {
        try {
            const siteKeyElement = await this.driver.findElement(By.css('[data-sitekey]'));
            return await siteKeyElement.getAttribute('data-sitekey');
        } catch (error) {
            const pageSource = await this.driver.getPageSource();

            let siteKeyMatch = pageSource.match(/data-sitekey="([^"]+)"/);
            if (siteKeyMatch) {
                return siteKeyMatch[1];
            }

            siteKeyMatch = pageSource.match(/sitekey=([a-f0-9-]{36})/);
            if (siteKeyMatch) {
                return siteKeyMatch[1];
            }

            siteKeyMatch = pageSource.match(/sitekey:\s*["']([^"']+)["']/);
            if (siteKeyMatch) {
                return siteKeyMatch[1];
            }

            const hcaptchaIframes = await this.driver.findElements(By.css('iframe[src*="hcaptcha"]'));
            for (const iframe of hcaptchaIframes) {
                const src = await iframe.getAttribute('src');
                const urlSiteKeyMatch = src.match(/sitekey=([a-f0-9-]{36})/);
                if (urlSiteKeyMatch) {
                    return urlSiteKeyMatch[1];
                }
            }

            throw new Error('Could not find captcha site key');
        }
    }

    async solveCaptcha() {
        console.log('Looking for captcha...');

        const captchaElement = await this.findCaptcha();
        if (!captchaElement) {
            console.log('No captcha found');
            return false;
        }

        console.log('Captcha found, extracting site key...');
        const siteKey = await this.extractSiteKey();
        const currentUrl = await this.driver.getCurrentUrl();

        console.log(`Site key: ${siteKey}`);

        const isHcaptcha = await this.driver.findElements(By.css('iframe[src*="hcaptcha"], .h-captcha, div[class*="hcaptcha"]'));
        const captchaType = isHcaptcha.length > 0 ? 'hcaptcha' : 'recaptchav2';

        console.log(`Captcha type: ${captchaType}`);
        console.log('Submitting captcha to SolveCaptcha...');

        const requestId = await this.submitCaptcha(siteKey, currentUrl, captchaType);
        console.log(`Request ID: ${requestId}`);

        console.log('Waiting for captcha solution...');
        const solution = await this.getCaptchaResult(requestId);
        console.log('Captcha solved!');

        if (captchaType === 'hcaptcha') {
            await this.driver.executeScript(`
                const responseElement = document.querySelector('[name="h-captcha-response"]') ||
                                      document.querySelector('textarea[name="h-captcha-response"]');
                if (responseElement) {
                    responseElement.innerHTML = '${solution}';
                    responseElement.value = '${solution}';
                }
                if (typeof hcaptcha !== 'undefined') {
                    hcaptcha.getResponse = function() { return '${solution}'; };
                }
            `);
        } else {
            await this.driver.executeScript(`
                const responseElement = document.getElementById('g-recaptcha-response');
                if (responseElement) {
                    responseElement.innerHTML = '${solution}';
                    responseElement.value = '${solution}';
                }
                if (typeof grecaptcha !== 'undefined') {
                    grecaptcha.getResponse = function() { return '${solution}'; };
                }
            `);
        }

        await this.sleep(1000);

        const submitButton = await this.driver.findElement(By.css('input[type="submit"], button[type="submit"], .submit-btn, #submit'));
        await submitButton.click();

        return true;
    }

    async loginToGamdom(username, password, config) {
        try {
            console.log('Navigating to Gamdom...');
            await this.driver.get(config.settings.target_url);
            await this.sleep(3000);

            console.log('Looking for login button...');
            const loginSelectors = config.selectors.login_button.join(', ');
            const loginButton = await this.driver.wait(
                until.elementLocated(By.css(loginSelectors)),
                this.waitTimeout
            );
            await loginButton.click();
            await this.sleep(2000);

            console.log('Entering credentials...');
            const usernameSelectors = config.selectors.username_field.join(', ');
            const usernameField = await this.driver.wait(
                until.elementLocated(By.css(usernameSelectors)),
                this.waitTimeout
            );
            await usernameField.clear();
            await usernameField.sendKeys(username);

            const passwordSelectors = config.selectors.password_field.join(', ');
            const passwordField = await this.driver.findElement(By.css(passwordSelectors));
            await passwordField.clear();
            await passwordField.sendKeys(password);

            await this.sleep(1000);

            const captchaSolved = await this.solveCaptcha();

            if (!captchaSolved) {
                const submitSelectors = config.selectors.submit_button.join(', ');
                const submitButton = await this.driver.findElement(By.css(submitSelectors));
                await submitButton.click();
            }

            await this.sleep(5000);

            const currentUrl = await this.driver.getCurrentUrl();
            if (currentUrl.includes('dashboard') || currentUrl.includes('account') || !currentUrl.includes('login')) {
                console.log('Login successful!');
                return true;
            } else {
                console.log('Login may have failed - still on login page');
                return false;
            }

        } catch (error) {
            console.error('Login error:', error.message);
            return false;
        }
    }

    async close() {
        if (this.driver) {
            await this.driver.quit();
        }
    }
}

async function main() {
    let config;
    try {
        config = JSON.parse(fs.readFileSync('config.json', 'utf8'));
    } catch (error) {
        console.error('Error reading config.json:', error.message);
        config = {
            credentials: {
                username: "oppenheimergang",
                password: "Z45@PKMG8JY2nfb",
                solvecaptcha_api_key: "59ff8b9c8f70d1cc677fb15c1a51ecb9"
            }
        };
    }

    const bypasser = new CaptchaBypasser(config.credentials.solvecaptcha_api_key);
    
    try {
        await bypasser.initializeDriver();
        const success = await bypasser.loginToGamdom(config.credentials.username, config.credentials.password, config);
        
        if (success) {
            console.log('Successfully logged in and bypassed captcha!');
            await bypasser.sleep(10000);
        } else {
            console.log('Login failed');
        }
        
    } catch (error) {
        console.error('Script error:', error.message);
    } finally {
        await bypasser.close();
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = CaptchaBypasser;
