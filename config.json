{"credentials": {"username": "oppenheimergang", "password": "Z45@PKMG8JY2nfb", "solvecaptcha_api_key": "59ff8b9c8f70d1cc677fb15c1a51ecb9"}, "settings": {"target_url": "https://gamdom.io/", "max_retries": 3, "wait_timeout": 30000, "poll_interval": 2000, "max_poll_time": 180000, "max_rounds_per_challenge": 2}, "selectors": {"login_button": ["button[data-testid=\"signin-nav\"]", "button[data-testid=\"login-button\"]", ".login-btn", "a[href*=\"login\"]", ".header-login"], "username_field": ["input[name=\"username\"]", "input[placeholder*=\"username\"]", "input[type=\"email\"]", "#username", ".username-input"], "password_field": ["input[name=\"password\"]", "input[placeholder*=\"password\"]", "input[type=\"password\"]", "#password", ".password-input"], "submit_button": ["button[data-testid=\"start-playing-login\"]", "button[type=\"submit\"]", "input[type=\"submit\"]", ".login-submit", ".submit-btn"], "captcha_elements": ["iframe[src*=\"hcaptcha\"]", ".h-cap<PERSON>a", "[data-sitekey]", "div[class*=\"hcaptcha\"]", "iframe[src*=\"recaptcha\"]", ".g-recaptcha", "div[class*=\"recaptcha\"]"]}}