# Captcha Bypasser

Automated browser login script with captcha solving capabilities using SolveCaptcha API.

## Features

- Automated browser control using Selenium WebDriver
- reCAPTCHA v2 solving via SolveCaptcha API
- Gamdom.io login automation
- Configurable retry and timeout settings
- Anti-detection measures

## Prerequisites

1. **Node.js** (version 14 or higher)
2. **Chrome browser** installed
3. **ChromeDriver** (will be automatically managed by selenium-webdriver)
4. **SolveCaptcha API key** (already configured in the script)

## Installation

1. Install dependencies:
```bash
npm install
```

## Usage

Run the script:
```bash
npm start
```

Or directly with Node:
```bash
node captcha-bypasser.js
```

## Configuration

The script is pre-configured with:
- Username: `oppenheimergang`
- Password: `Z45@PKMG8JY2nfb`
- SolveCaptcha API Key: `59ff8b9c8f70d1cc677fb15c1a51ecb9`
- Target site: `https://gamdom.io/`

## How it works

1. Initializes Chrome browser with anti-detection settings
2. Navigates to Gamdom.io
3. Finds and clicks login button
4. Enters username and password
5. Detects reCAPTCHA if present
6. Submits captcha to SolveCaptcha API
7. Waits for solution and injects it
8. Completes login process

## Troubleshooting

- Make sure Chrome browser is installed
- Ensure stable internet connection
- Check SolveCaptcha API balance
- Verify credentials are correct

## Notes

- The script includes random delays to appear more human-like
- Anti-detection measures are implemented
- Supports various captcha container selectors
- Automatically handles different login page layouts
